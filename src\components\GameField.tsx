
import React from 'react';
import { motion } from 'framer-motion';

const GameField = () => {
  return (
    <div className="relative w-full h-full overflow-hidden">
      {/* Stadium Sky with Realistic Atmosphere */}
      <div className="absolute inset-0 bg-gradient-to-b from-blue-800 via-blue-600 to-blue-400">
        {/* Stadium Lights */}
        <div className="absolute top-0 left-0 w-full h-32 bg-gradient-to-b from-gray-900 to-transparent opacity-70">
          <div className="flex justify-around items-center h-full">
            {[...Array(12)].map((_, i) => (
              <motion.div
                key={i}
                animate={{ 
                  opacity: [0.6, 1, 0.6],
                  scale: [1, 1.1, 1]
                }}
                transition={{ 
                  duration: 3, 
                  repeat: Infinity, 
                  delay: i * 0.3,
                  ease: "easeInOut"
                }}
                className="w-4 h-4 bg-yellow-100 rounded-full"
                style={{ 
                  boxShadow: '0 0 25px #fef9c3, 0 0 50px #fef9c3, 0 0 75px #fef9c3',
                  filter: 'blur(1px)'
                }}
              />
            ))}
          </div>
        </div>

        {/* Stadium Crowd Silhouette */}
        <div className="absolute top-24 left-0 w-full h-48 bg-gradient-to-b from-gray-800 to-transparent opacity-60">
          <motion.div 
            className="w-full h-full"
            animate={{ opacity: [0.6, 0.8, 0.6] }}
            transition={{ duration: 4, repeat: Infinity }}
            style={{
              backgroundImage: `
                repeating-linear-gradient(90deg, 
                  #1f2937 0px, #1f2937 8px, 
                  #374151 8px, #374151 16px
                ),
                repeating-linear-gradient(0deg, 
                  transparent 0px, transparent 20px, 
                  rgba(0,0,0,0.3) 20px, rgba(0,0,0,0.3) 25px
                )
              `,
              backgroundSize: '16px 25px'
            }}
          />
        </div>
      </div>

      {/* Premium NFL Stadium Football Field */}
      <div 
        className="absolute bottom-0 w-full h-3/4"
        style={{
          background: `
            linear-gradient(180deg, 
              rgba(34, 197, 94, 0.95) 0%, 
              rgba(22, 163, 74, 1) 20%, 
              rgba(21, 128, 61, 1) 60%,
              rgba(20, 83, 45, 1) 100%
            )
          `,
          transform: 'perspective(1200px) rotateX(55deg)',
          transformOrigin: 'bottom center'
        }}
      >
        {/* Realistic Field Lines and NFL Markings */}
        <div className="absolute inset-0" style={{
          backgroundImage: `
            repeating-linear-gradient(
              0deg,
              transparent 0px,
              transparent 46px,
              rgba(255, 255, 255, 0.95) 46px,
              rgba(255, 255, 255, 0.95) 50px
            ),
            repeating-linear-gradient(
              90deg,
              transparent 0px,
              transparent 96px,
              rgba(255, 255, 255, 0.8) 96px,
              rgba(255, 255, 255, 0.8) 100px
            )
          `
        }}>
          
          {/* NFL Yard Numbers */}
          {[10, 20, 30, 40, 50, 40, 30, 20, 10].map((yard, i) => (
            <div
              key={i}
              className="absolute text-white font-black opacity-90"
              style={{
                bottom: `${8 + i * 9}%`,
                left: '12%',
                fontSize: '28px',
                textShadow: '3px 3px 6px rgba(0,0,0,0.8)',
                fontFamily: 'Arial Black, sans-serif',
                transform: 'rotateX(-55deg) scale(1.2)',
                letterSpacing: '2px'
              }}
            >
              {yard}
            </div>
          ))}
          
          {/* Right side yard numbers */}
          {[10, 20, 30, 40, 50, 40, 30, 20, 10].map((yard, i) => (
            <div
              key={`right-${i}`}
              className="absolute text-white font-black opacity-90"
              style={{
                bottom: `${8 + i * 9}%`,
                right: '12%',
                fontSize: '28px',
                textShadow: '3px 3px 6px rgba(0,0,0,0.8)',
                fontFamily: 'Arial Black, sans-serif',
                transform: 'rotateX(-55deg) rotateY(180deg) scale(1.2)',
                letterSpacing: '2px'
              }}
            >
              {yard}
            </div>
          ))}
          
          {/* Hash Marks - Left Side */}
          <div className="absolute inset-0">
            {[...Array(22)].map((_, i) => (
              <div
                key={`left-${i}`}
                className="absolute w-12 h-1 bg-white opacity-80"
                style={{
                  bottom: `${4 + i * 4.2}%`,
                  left: '28%',
                  transform: 'rotateX(-55deg)'
                }}
              />
            ))}
          </div>
          
          {/* Hash Marks - Right Side */}
          <div className="absolute inset-0">
            {[...Array(22)].map((_, i) => (
              <div
                key={`right-${i}`}
                className="absolute w-12 h-1 bg-white opacity-80"
                style={{
                  bottom: `${4 + i * 4.2}%`,
                  right: '28%',
                  transform: 'rotateX(-55deg)'
                }}
              />
            ))}
          </div>
        </div>

        {/* End Zones with Team Colors */}
        <div 
          className="absolute bottom-0 w-full h-24 opacity-90"
          style={{
            background: 'linear-gradient(90deg, #1e40af 0%, #3b82f6 50%, #1e40af 100%)'
          }}
        >
          <div className="absolute inset-0 flex items-center justify-center">
            <span 
              className="text-white font-black tracking-widest transform -skew-y-6"
              style={{
                fontSize: '32px',
                textShadow: '4px 4px 8px rgba(0,0,0,0.8)',
                letterSpacing: '4px'
              }}
            >
              SCROLLFIT
            </span>
          </div>
        </div>

        <div 
          className="absolute top-0 w-full h-24 opacity-90"
          style={{
            background: 'linear-gradient(90deg, #dc2626 0%, #ef4444 50%, #dc2626 100%)'
          }}
        >
          <div className="absolute inset-0 flex items-center justify-center">
            <span 
              className="text-white font-black tracking-widest transform -skew-y-6"
              style={{
                fontSize: '32px',
                textShadow: '4px 4px 8px rgba(0,0,0,0.8)',
                letterSpacing: '4px'
              }}
            >
              FITNESS
            </span>
          </div>
        </div>

        {/* Improved Three Running Lanes with Safe Margins */}
        <div className="absolute inset-0 flex" style={{ paddingLeft: '10%', paddingRight: '10%' }}>
          {/* Left Lane - 25% position */}
          <div
            className="flex-1 border-r-2 border-white border-dashed opacity-40"
            style={{ 
              borderRightWidth: 2,
              borderStyle: 'dashed'
            }}
          />
          {/* Center Lane - 50% position */}
          <div
            className="flex-1 border-r-2 border-white border-dashed opacity-40"
            style={{ 
              borderRightWidth: 2,
              borderStyle: 'dashed'
            }}
          />
          {/* Right Lane - 75% position */}
          <div className="flex-1" />
        </div>

        {/* Center Field Logo */}
        <div 
          className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2"
          style={{ transform: 'translate(-50%, -50%) rotateX(-55deg)' }}
        >
          <motion.div
            animate={{ 
              rotate: 360,
              scale: [1, 1.1, 1]
            }}
            transition={{ 
              rotate: { duration: 20, repeat: Infinity, ease: "linear" },
              scale: { duration: 3, repeat: Infinity }
            }}
            className="w-16 h-16 bg-white rounded-full flex items-center justify-center shadow-2xl border-4 border-blue-600"
          >
            <span className="text-2xl">🏈</span>
          </motion.div>
        </div>
      </div>

      {/* Realistic Grass Texture and Wind Effects */}
      <div 
        className="absolute bottom-0 w-full h-3/4 opacity-20 pointer-events-none"
        style={{
          backgroundImage: `
            radial-gradient(circle at 20% 50%, rgba(34, 197, 94, 0.4) 0%, transparent 50%),
            radial-gradient(circle at 80% 30%, rgba(21, 128, 61, 0.4) 0%, transparent 50%),
            radial-gradient(circle at 60% 80%, rgba(22, 163, 74, 0.3) 0%, transparent 40%),
            repeating-linear-gradient(
              47deg,
              transparent,
              transparent 2px,
              rgba(255, 255, 255, 0.06) 2px,
              rgba(255, 255, 255, 0.06) 4px
            )
          `,
          transform: 'perspective(1200px) rotateX(55deg)',
          transformOrigin: 'bottom center'
        }}
      />

      {/* Stadium Atmosphere Particles */}
      <div className="absolute inset-0 pointer-events-none overflow-hidden">
        {[...Array(8)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-1 h-1 bg-white rounded-full opacity-60"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 40 + 20}%`
            }}
            animate={{
              y: [0, -100, -200],
              opacity: [0, 0.6, 0],
              scale: [0, 1, 0.5, 0]
            }}
            transition={{
              duration: 4 + Math.random() * 3,
              repeat: Infinity,
              delay: Math.random() * 5,
              ease: "easeOut"
            }}
          />
        ))}
      </div>
    </div>
  );
};

export default GameField;
