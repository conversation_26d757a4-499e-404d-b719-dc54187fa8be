
import React from 'react';
import { motion } from 'framer-motion';
import { ArrowLeft, Lock, Crown, Star } from 'lucide-react';

interface CharacterSelectionProps {
  selectedSprite: number;
  onSpriteSelect: (spriteId: number) => void;
  onBack: () => void;
}

const CharacterSelection = ({ selectedSprite, onSpriteSelect, onBack }: CharacterSelectionProps) => {
  const sprites = [
    { id: 0, src: '/lovable-uploads/de160ff6-207c-40ea-a340-dd26f1824f75.png', name: 'Green Warrior', unlocked: true, rarity: 'common' },
    { id: 1, src: '/lovable-uploads/e74579c3-0c3b-42b2-8fbb-4ea923e0d4ac.png', name: 'Red Thunder', unlocked: true, rarity: 'common' },
    { id: 2, src: '/lovable-uploads/4c73eaf9-d066-4572-a35a-9892d2556bb5.png', name: '<PERSON> <PERSON>', unlocked: true, rarity: 'rare' },
    { id: 3, src: '/lovable-uploads/9cbe91da-dc2b-472f-a079-a7256b30e52a.png', name: 'Purple Storm', unlocked: true, rarity: 'rare' },
    { id: 4, src: '/lovable-uploads/f35d2efa-b3f9-46f7-b3ac-03293ae08729.png', name: 'Orange Fire', unlocked: false, rarity: 'epic', requirement: 'Score 5000+ points' },
    { id: 5, src: '/lovable-uploads/1625f81a-abce-40ef-a214-dacdc5a60388.png', name: 'Forest Guardian', unlocked: false, rarity: 'epic', requirement: 'Play 50 games' },
    { id: 6, src: '/lovable-uploads/c27ceaa2-cfdb-472b-944e-7c09b313a302.png', name: 'Cardinal Rush', unlocked: false, rarity: 'legendary', requirement: 'Win 20 games' },
    { id: 7, src: '/lovable-uploads/ede3c27f-0d43-4dd3-9048-9d90e5b87669.png', name: 'Ocean Blue', unlocked: false, rarity: 'legendary', requirement: 'Collect 500 items' },
    { id: 8, src: '/lovable-uploads/780a52af-4f53-4651-9169-cdfe7b8025bf.png', name: 'Royal Purple', unlocked: false, rarity: 'mythic', requirement: 'Reach Level 25' },
    { id: 9, src: '/lovable-uploads/7ba0c46b-0c26-4d7f-a2b4-4bb231a1adf6.png', name: 'Crimson Elite', unlocked: false, rarity: 'mythic', requirement: 'Complete all achievements' }
  ];

  const getRarityColor = (rarity: string) => {
    switch (rarity) {
      case 'common': return '#9CA3AF';
      case 'rare': return '#3B82F6';
      case 'epic': return '#8B5CF6';
      case 'legendary': return '#F59E0B';
      case 'mythic': return '#EF4444';
      default: return '#9CA3AF';
    }
  };

  const getRarityGlow = (rarity: string) => {
    switch (rarity) {
      case 'common': return '0 0 20px rgba(156, 163, 175, 0.5)';
      case 'rare': return '0 0 30px rgba(59, 130, 246, 0.7)';
      case 'epic': return '0 0 40px rgba(139, 92, 246, 0.8)';
      case 'legendary': return '0 0 50px rgba(245, 158, 11, 0.9)';
      case 'mythic': return '0 0 60px rgba(239, 68, 68, 1)';
      default: return 'none';
    }
  };

  return (
    <div className="h-screen w-full relative overflow-hidden flex flex-col" style={{
      background: 'linear-gradient(135deg, #0A0B1A 0%, #161B33 50%, #0D1117 100%)'
    }}>
      {/* Enhanced Gaming Background */}
      <div className="absolute inset-0 overflow-hidden">
        {[...Array(40)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute rounded-full"
            style={{
              width: Math.random() * 3 + 1,
              height: Math.random() * 3 + 1,
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              background: i % 4 === 0 ? '#39FF14' : i % 4 === 1 ? '#00D4FF' : i % 4 === 2 ? '#FFD700' : '#FF6B35'
            }}
            animate={{
              opacity: [0.2, 0.8, 0.2],
              scale: [1, 2, 1],
              y: [0, -30, 0]
            }}
            transition={{
              duration: 3 + Math.random() * 2,
              repeat: Infinity,
              delay: Math.random() * 3,
            }}
          />
        ))}
      </div>

      <div className="relative z-10 h-full flex flex-col">
        {/* Compact Header */}
        <motion.div
          initial={{ opacity: 0, y: -50 }}
          animate={{ opacity: 1, y: 0 }}
          className="pt-6 px-4 mb-4 flex-shrink-0"
        >
          <div className="flex items-center justify-between mb-4">
            <motion.button
              whileHover={{ scale: 1.1, boxShadow: '0 0 25px rgba(57, 255, 20, 0.5)' }}
              whileTap={{ scale: 0.9 }}
              onClick={onBack}
              className="w-12 h-12 rounded-full flex items-center justify-center border-2 border-[#39FF14]"
              style={{
                background: 'linear-gradient(135deg, rgba(57, 255, 20, 0.2), rgba(0, 212, 255, 0.2))',
                backdropFilter: 'blur(20px)'
              }}
            >
              <ArrowLeft className="w-6 h-6 text-[#39FF14]" />
            </motion.button>

            <div className="text-center">
              <motion.h1 
                className="text-2xl font-black text-white mb-1"
                animate={{
                  textShadow: [
                    '0 0 20px #00D4FF',
                    '0 0 40px #00D4FF, 0 0 60px #39FF14',
                    '0 0 20px #00D4FF'
                  ]
                }}
                transition={{ duration: 3, repeat: Infinity }}
              >
                ⚔️ CHOOSE YOUR WARRIOR ⚔️
              </motion.h1>
              <p className="text-[#39FF14] font-bold text-sm">Select your champion fighter</p>
            </div>

            <div className="w-12" />
          </div>
        </motion.div>

        {/* Character Grid - Fixed Height, All Visible */}
        <div className="flex-1 px-4 pb-4">
          <style>
            {`
              .hide-scrollbar-warriors {
                -webkit-overflow-scrolling: touch;
                scrollbar-width: none;
                -ms-overflow-style: none;
              }
              .hide-scrollbar-warriors::-webkit-scrollbar {
                display: none;
              }
            `}
          </style>
          
          <div className="h-full overflow-y-auto hide-scrollbar-warriors">
            <div className="grid grid-cols-2 gap-3 pb-4">
              {sprites.map((sprite, index) => (
                <motion.div
                  key={sprite.id}
                  initial={{ opacity: 0, scale: 0.8, y: 50 }}
                  animate={{ opacity: 1, scale: 1, y: 0 }}
                  transition={{ delay: index * 0.05 }}
                  whileHover={{ 
                    scale: sprite.unlocked ? 1.02 : 1,
                    y: sprite.unlocked ? -2 : 0
                  }}
                  whileTap={{ scale: sprite.unlocked ? 0.98 : 1 }}
                  onClick={() => sprite.unlocked && onSpriteSelect(sprite.id)}
                  className={`relative cursor-pointer ${sprite.unlocked ? '' : 'cursor-not-allowed'}`}
                >
                  <div 
                    className={`p-3 rounded-2xl border-2 relative overflow-hidden ${
                      selectedSprite === sprite.id && sprite.unlocked
                        ? 'border-[#39FF14]' 
                        : sprite.unlocked 
                        ? 'border-white/20' 
                        : 'border-gray-600/30'
                    }`}
                    style={{
                      background: sprite.unlocked 
                        ? `linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(${getRarityColor(sprite.rarity).slice(1)}, 0.1))` 
                        : 'rgba(0, 0, 0, 0.4)',
                      backdropFilter: 'blur(25px)',
                      boxShadow: selectedSprite === sprite.id && sprite.unlocked 
                        ? `0 0 30px rgba(57, 255, 20, 0.6), ${getRarityGlow(sprite.rarity)}`
                        : sprite.unlocked 
                        ? getRarityGlow(sprite.rarity)
                        : 'none',
                      minHeight: '180px'
                    }}
                  >
                    {/* Selection Indicator */}
                    {selectedSprite === sprite.id && sprite.unlocked && (
                      <motion.div
                        initial={{ scale: 0 }}
                        animate={{ scale: 1, rotate: 360 }}
                        className="absolute top-2 right-2 w-6 h-6 bg-[#39FF14] rounded-full flex items-center justify-center"
                        style={{ boxShadow: '0 0 15px rgba(57, 255, 20, 0.8)' }}
                      >
                        <div className="w-3 h-3 bg-black rounded-full" />
                      </motion.div>
                    )}

                    {/* Lock Indicator */}
                    {!sprite.unlocked && (
                      <div className="absolute top-2 right-2 w-6 h-6 bg-gray-600 rounded-full flex items-center justify-center">
                        <Lock className="w-3 h-3 text-white" />
                      </div>
                    )}

                    {/* Rarity Badge */}
                    <div className="absolute top-2 left-2 flex items-center space-x-1">
                      {sprite.rarity === 'legendary' && (
                        <motion.div
                          animate={{ rotate: [0, 15, -15, 0] }}
                          transition={{ duration: 2, repeat: Infinity }}
                        >
                          <Crown className="w-4 h-4" style={{ color: getRarityColor(sprite.rarity) }} />
                        </motion.div>
                      )}
                      {sprite.rarity === 'mythic' && (
                        <motion.div
                          animate={{ 
                            rotate: 360,
                            scale: [1, 1.2, 1]
                          }}
                          transition={{ 
                            rotate: { duration: 3, repeat: Infinity },
                            scale: { duration: 2, repeat: Infinity }
                          }}
                        >
                          <Star className="w-4 h-4" style={{ color: getRarityColor(sprite.rarity) }} />
                        </motion.div>
                      )}
                    </div>

                    {/* Character Image */}
                    <div className="relative mb-3 mt-6">
                      <motion.img
                        src={sprite.src}
                        alt={sprite.name}
                        className={`w-full h-24 object-contain mx-auto ${
                          sprite.unlocked ? '' : 'grayscale opacity-50'
                        }`}
                        style={{
                          imageRendering: 'pixelated',
                          filter: sprite.unlocked 
                            ? `contrast(1.2) saturate(1.3) ${selectedSprite === sprite.id ? 'drop-shadow(0 0 20px #39FF14)' : ''}` 
                            : 'grayscale(100%) opacity(0.5)'
                        }}
                        animate={selectedSprite === sprite.id && sprite.unlocked ? {
                          scale: [1, 1.05, 1],
                          filter: [
                            'contrast(1.2) saturate(1.3) drop-shadow(0 0 20px #39FF14)',
                            'contrast(1.3) saturate(1.4) drop-shadow(0 0 25px #39FF14)',
                            'contrast(1.2) saturate(1.3) drop-shadow(0 0 20px #39FF14)'
                          ]
                        } : {}}
                        transition={{ duration: 2, repeat: Infinity }}
                      />
                    </div>

                    {/* Character Info */}
                    <div className="text-center relative z-10">
                      <h3 className={`font-black text-sm mb-1 ${
                        sprite.unlocked ? 'text-white' : 'text-gray-500'
                      }`}>
                        {sprite.name}
                      </h3>
                      
                      <div 
                        className="text-xs font-black uppercase tracking-wider mb-2"
                        style={{ color: getRarityColor(sprite.rarity) }}
                      >
                        {sprite.rarity}
                      </div>

                      {!sprite.unlocked && sprite.requirement && (
                        <p className="text-xs text-gray-400 leading-tight px-1">
                          {sprite.requirement}
                        </p>
                      )}

                      {sprite.unlocked && selectedSprite === sprite.id && (
                        <motion.div
                          initial={{ opacity: 0, scale: 0 }}
                          animate={{ opacity: 1, scale: 1 }}
                          className="mt-2 px-3 py-1 bg-gradient-to-r from-[#39FF14] to-[#00FF88] text-black text-xs font-black rounded-full"
                        >
                          ⚡ SELECTED ⚡
                        </motion.div>
                      )}
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CharacterSelection;
