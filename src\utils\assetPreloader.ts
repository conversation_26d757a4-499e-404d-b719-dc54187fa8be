/**
 * Asset Preloader for ScrollFit Game
 * Ensures all critical assets are loaded before game starts
 * Optimized for online mobile gaming with zero delays
 */

interface PreloadedAsset {
  url: string;
  type: 'image' | 'audio' | 'font';
  loaded: boolean;
  element?: HTMLImageElement | HTMLAudioElement;
}

class AssetPreloader {
  private assets: Map<string, PreloadedAsset> = new Map();
  private loadingPromises: Promise<void>[] = [];

  /**
   * Add an asset to the preload queue
   */
  addAsset(url: string, type: 'image' | 'audio' | 'font' = 'image'): void {
    if (this.assets.has(url)) return;

    const asset: PreloadedAsset = {
      url,
      type,
      loaded: false
    };

    this.assets.set(url, asset);
  }

  /**
   * Preload a single image
   */
  private preloadImage(url: string): Promise<void> {
    return new Promise((resolve, reject) => {
      const img = new Image();
      
      img.onload = () => {
        const asset = this.assets.get(url);
        if (asset) {
          asset.loaded = true;
          asset.element = img;
        }
        resolve();
      };

      img.onerror = () => {
        console.warn(`Failed to load image: ${url}`);
        reject(new Error(`Failed to load image: ${url}`));
      };

      // Set crossOrigin for external images
      if (url.startsWith('http')) {
        img.crossOrigin = 'anonymous';
      }

      img.src = url;
    });
  }

  /**
   * Preload a single audio file
   */
  private preloadAudio(url: string): Promise<void> {
    return new Promise((resolve, reject) => {
      const audio = new Audio();
      
      audio.oncanplaythrough = () => {
        const asset = this.assets.get(url);
        if (asset) {
          asset.loaded = true;
          asset.element = audio;
        }
        resolve();
      };

      audio.onerror = () => {
        console.warn(`Failed to load audio: ${url}`);
        reject(new Error(`Failed to load audio: ${url}`));
      };

      audio.src = url;
      audio.load();
    });
  }

  /**
   * Preload a font
   */
  private preloadFont(url: string): Promise<void> {
    return new Promise((resolve, reject) => {
      const link = document.createElement('link');
      link.rel = 'preload';
      link.as = 'font';
      link.type = 'font/woff2';
      link.crossOrigin = 'anonymous';
      
      link.onload = () => {
        const asset = this.assets.get(url);
        if (asset) {
          asset.loaded = true;
        }
        resolve();
      };

      link.onerror = () => {
        console.warn(`Failed to load font: ${url}`);
        reject(new Error(`Failed to load font: ${url}`));
      };

      link.href = url;
      document.head.appendChild(link);
    });
  }

  /**
   * Start preloading all assets
   */
  async preloadAll(): Promise<void> {
    this.loadingPromises = [];

    for (const [url, asset] of this.assets) {
      if (asset.loaded) continue;

      let promise: Promise<void>;

      switch (asset.type) {
        case 'image':
          promise = this.preloadImage(url);
          break;
        case 'audio':
          promise = this.preloadAudio(url);
          break;
        case 'font':
          promise = this.preloadFont(url);
          break;
        default:
          promise = this.preloadImage(url);
      }

      // Don't let individual asset failures stop the entire loading process
      const safePromise = promise.catch(error => {
        console.warn(`Asset loading failed: ${url}`, error);
      });

      this.loadingPromises.push(safePromise);
    }

    await Promise.all(this.loadingPromises);
  }

  /**
   * Get loading progress (0-100)
   */
  getProgress(): number {
    const total = this.assets.size;
    if (total === 0) return 100;

    const loaded = Array.from(this.assets.values()).filter(asset => asset.loaded).length;
    return Math.round((loaded / total) * 100);
  }

  /**
   * Check if all assets are loaded
   */
  isComplete(): boolean {
    return Array.from(this.assets.values()).every(asset => asset.loaded);
  }

  /**
   * Get a preloaded asset element
   */
  getAsset(url: string): HTMLImageElement | HTMLAudioElement | null {
    const asset = this.assets.get(url);
    return asset?.element || null;
  }

  /**
   * Clear all assets
   */
  clear(): void {
    this.assets.clear();
    this.loadingPromises = [];
  }
}

// Create singleton instance
export const assetPreloader = new AssetPreloader();

// Game-specific asset preloader for offline game
export const preloadGameAssets = async (): Promise<void> => {
  // Critical game assets for offline play
  const criticalAssets = [
    '/scrollfit.png', // Main splash image
    '/lovable-uploads/scrollfitt.mp4', // Background video
    // New player sprites
    '/lovable-uploads/player_sprites/sprite_1.png',
    '/lovable-uploads/player_sprites/sprite_2.png',
    '/lovable-uploads/player_sprites/sprite_3.png',
    '/lovable-uploads/player_sprites/sprite_4.png',
    '/lovable-uploads/player_sprites/sprite_5.png',
    '/lovable-uploads/player_sprites/sprite_6.png',
    '/lovable-uploads/player_sprites/sprite_7.png',
    '/lovable-uploads/player_sprites/sprite_8.png',
    '/lovable-uploads/player_sprites/sprite_9.png',
    '/lovable-uploads/player_sprites/sprite_10.png',
    '/lovable-uploads/player_sprites/sprite_11.png',
    '/lovable-uploads/player_sprites/sprite_12.png',
  ];

  // Add all critical assets to preloader
  criticalAssets.forEach(url => {
    if (url.endsWith('.mp4')) {
      // For video files, we'll handle them separately
      console.log('Video asset detected:', url);
    } else {
      assetPreloader.addAsset(url, 'image');
    }
  });

  // Preload all assets
  await assetPreloader.preloadAll();
};

// Preload all game components and their dependencies
export const preloadGameComponents = async (): Promise<void> => {
  try {
    // Preload critical game assets first
    await preloadGameAssets();

    // Simulate component initialization and dependency loading
    // This ensures all React components are ready and no rendering delays occur
    const componentLoadingTasks = [
      // Simulate loading game field textures and sprites
      new Promise(resolve => setTimeout(resolve, 200)),
      // Simulate loading player sprites and animations
      new Promise(resolve => setTimeout(resolve, 300)),
      // Simulate loading collectible assets
      new Promise(resolve => setTimeout(resolve, 250)),
      // Simulate loading UI components and fonts
      new Promise(resolve => setTimeout(resolve, 150)),
      // Simulate loading sound effects (for future use)
      new Promise(resolve => setTimeout(resolve, 100)),
      // Simulate loading game state and save data
      new Promise(resolve => setTimeout(resolve, 100)),
    ];

    // Wait for all components to be ready
    await Promise.all(componentLoadingTasks);

    console.log('All game components preloaded successfully');
  } catch (error) {
    console.warn('Some game components failed to preload:', error);
  }
};

export default assetPreloader;
