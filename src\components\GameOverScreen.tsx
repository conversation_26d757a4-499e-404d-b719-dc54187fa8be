
import React from 'react';
import { motion } from 'framer-motion';
import { Trophy, RotateCcw, Home, Star, Award, Target } from 'lucide-react';

interface GameOverScreenProps {
  score: number;
  highScore: number;
  coins: number;
  onRestart: () => void;
  onMainMenu: () => void;
  isNewHighScore: boolean;
}

const GameOverScreen = ({ 
  score, 
  highScore, 
  coins, 
  onRestart, 
  onMainMenu, 
  isNewHighScore 
}: GameOverScreenProps) => {
  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      className="absolute inset-0 z-50"
      style={{
        background: 'linear-gradient(135deg, rgba(10, 11, 26, 0.95) 0%, rgba(22, 27, 51, 0.95) 50%, rgba(13, 17, 23, 0.95) 100%)',
        backdropFilter: 'blur(20px)'
      }}
    >
      <div className="flex items-center justify-center min-h-screen p-6">
        <motion.div
          initial={{ scale: 0, rotateY: 180 }}
          animate={{ scale: 1, rotateY: 0 }}
          transition={{ duration: 0.8, type: "spring", bounce: 0.4 }}
          className="w-full max-w-md"
        >
          <div 
            className="relative p-8 rounded-3xl border border-white/20"
            style={{
              background: 'rgba(255, 255, 255, 0.05)',
              backdropFilter: 'blur(25px)',
              boxShadow: isNewHighScore 
                ? '0 20px 60px rgba(57, 255, 20, 0.3), 0 8px 32px rgba(0, 0, 0, 0.4)'
                : '0 8px 32px rgba(0, 0, 0, 0.4)'
            }}
          >
            {/* New High Score Glow Effect */}
            {isNewHighScore && (
              <motion.div
                className="absolute -inset-1 rounded-3xl opacity-75"
                animate={{
                  background: [
                    'linear-gradient(45deg, #39FF14, #00FF88)',
                    'linear-gradient(45deg, #00FF88, #39FF14)',
                    'linear-gradient(45deg, #39FF14, #00FF88)'
                  ]
                }}
                transition={{
                  duration: 2,
                  repeat: Infinity,
                }}
                style={{ zIndex: -1 }}
              />
            )}

            {/* Header */}
            <motion.div
              className="text-center mb-8"
              animate={isNewHighScore ? {
                scale: [1, 1.05, 1],
              } : {}}
              transition={{ duration: 1, repeat: Infinity }}
            >
              {isNewHighScore ? (
                <>
                  <motion.div
                    animate={{ rotate: [0, 10, -10, 0] }}
                    transition={{ duration: 0.6, repeat: Infinity }}
                    className="text-6xl mb-4"
                  >
                    🏆
                  </motion.div>
                  <h2 
                    className="text-3xl font-black mb-2"
                    style={{ 
                      background: 'linear-gradient(45deg, #39FF14, #00FF88)',
                      WebkitBackgroundClip: 'text',
                      WebkitTextFillColor: 'transparent',
                      textShadow: '0 0 30px rgba(57, 255, 20, 0.5)'
                    }}
                  >
                    NEW RECORD!
                  </h2>
                  <p className="text-[#39FF14] font-bold">Incredible performance, champion!</p>
                </>
              ) : (
                <>
                  <div className="text-5xl mb-4">🏈</div>
                  <h2 className="text-3xl font-black text-white mb-2">GAME OVER</h2>
                  <p className="text-[#B8BCC8]">Great effort, keep training!</p>
                </>
              )}
            </motion.div>

            {/* Stats Cards */}
            <div className="space-y-4 mb-8">
              {/* Final Score */}
              <motion.div
                initial={{ x: -50, opacity: 0 }}
                animate={{ x: 0, opacity: 1 }}
                transition={{ delay: 0.2 }}
                className="p-4 rounded-2xl border border-white/10"
                style={{
                  background: 'linear-gradient(145deg, rgba(0, 212, 255, 0.1), rgba(74, 158, 255, 0.1))',
                  backdropFilter: 'blur(15px)'
                }}
              >
                <div className="flex items-center justify-between">
                  <div>
                    <div className="text-[#B8BCC8] text-sm mb-1">FINAL SCORE</div>
                    <div className="text-2xl font-black text-white">{score.toLocaleString()}</div>
                  </div>
                  <Target className="w-8 h-8 text-[#00D4FF]" />
                </div>
              </motion.div>

              {/* High Score */}
              <motion.div
                initial={{ x: 50, opacity: 0 }}
                animate={{ x: 0, opacity: 1 }}
                transition={{ delay: 0.3 }}
                className="p-4 rounded-2xl border border-white/10"
                style={{
                  background: 'linear-gradient(145deg, rgba(255, 215, 0, 0.1), rgba(255, 165, 0, 0.1))',
                  backdropFilter: 'blur(15px)'
                }}
              >
                <div className="flex items-center justify-between">
                  <div>
                    <div className="text-[#B8BCC8] text-sm mb-1">PERSONAL BEST</div>
                    <div className="text-2xl font-black text-white">{highScore.toLocaleString()}</div>
                  </div>
                  <Trophy className="w-8 h-8 text-[#FFD700]" />
                </div>
              </motion.div>

              {/* Coins Earned */}
              <motion.div
                initial={{ y: 50, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 0.4 }}
                className="p-4 rounded-2xl border border-white/10"
                style={{
                  background: 'linear-gradient(145deg, rgba(0, 255, 127, 0.1), rgba(50, 205, 50, 0.1))',
                  backdropFilter: 'blur(15px)'
                }}
              >
                <div className="flex items-center justify-between">
                  <div>
                    <div className="text-[#B8BCC8] text-sm mb-1">COINS EARNED</div>
                    <div className="text-2xl font-black text-white">{coins}</div>
                  </div>
                  <motion.div
                    animate={{ rotate: 360 }}
                    transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                    className="text-3xl"
                  >
                    🪙
                  </motion.div>
                </div>
              </motion.div>
            </div>

            {/* Action Buttons */}
            <div className="space-y-3">
              <motion.button
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ delay: 0.6 }}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={onRestart}
                className="w-full py-4 rounded-2xl font-bold text-lg flex items-center justify-center space-x-3"
                style={{
                  background: 'linear-gradient(45deg, #39FF14, #00FF88)',
                  boxShadow: '0 0 25px rgba(57, 255, 20, 0.4)',
                  color: '#000'
                }}
              >
                <RotateCcw className="w-5 h-5" />
                <span>PLAY AGAIN</span>
              </motion.button>
              
              <motion.button
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ delay: 0.7 }}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={onMainMenu}
                className="w-full py-4 rounded-2xl font-bold text-lg flex items-center justify-center space-x-3 border border-white/20 text-white"
                style={{
                  background: 'rgba(255, 255, 255, 0.1)',
                  backdropFilter: 'blur(15px)'
                }}
              >
                <Home className="w-5 h-5" />
                <span>MAIN MENU</span>
              </motion.button>
            </div>

            {/* Achievement Hint */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 1 }}
              className="mt-6 text-center"
            >
              <div className="flex items-center justify-center space-x-2 text-[#B8BCC8] text-sm">
                {score > 1000 ? (
                  <>
                    <Star className="w-4 h-4 text-[#FFD700]" />
                    <span>Elite Performance! 🔥</span>
                  </>
                ) : score > 500 ? (
                  <>
                    <Award className="w-4 h-4 text-[#00D4FF]" />
                    <span>Solid run! Keep pushing! 💪</span>
                  </>
                ) : (
                  <>
                    <Target className="w-4 h-4 text-[#9CA3AF]" />
                    <span>Every champion started somewhere! 🎯</span>
                  </>
                )}
              </div>
            </motion.div>

            {/* Floating Particles */}
            {isNewHighScore && (
              <div className="absolute inset-0 pointer-events-none">
                {[...Array(20)].map((_, i) => (
                  <motion.div
                    key={i}
                    className="absolute w-2 h-2 bg-[#39FF14] rounded-full"
                    style={{
                      left: `${Math.random() * 100}%`,
                      top: `${Math.random() * 100}%`,
                    }}
                    animate={{
                      y: [0, -100],
                      opacity: [1, 0],
                      scale: [1, 0],
                    }}
                    transition={{
                      duration: 2,
                      repeat: Infinity,
                      delay: Math.random() * 2,
                    }}
                  />
                ))}
              </div>
            )}
          </div>
        </motion.div>
      </div>
    </motion.div>
  );
};

export default GameOverScreen;
