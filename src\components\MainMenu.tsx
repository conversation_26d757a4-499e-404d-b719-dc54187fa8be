
import React, { useState } from 'react';
import { motion } from 'framer-motion';
import CharacterSelection from './CharacterSelection';
import StatsPage from './StatsPage';
import SettingsPage from './SettingsPage';
import TrophiesPage from './TrophiesPage';
import PlayerAccount from './PlayerAccount';
import { User, Trophy, BarChart3, Settings, Play, Crown, UserCog } from 'lucide-react';

interface MainMenuProps {
  onStartGame: () => void;
  onCustomize: () => void;
  onAccount?: () => void;
  highScore: number;
  selectedSprite: number;
  onSpriteSelect: (spriteId: number) => void;
}

type ActivePage = 'main' | 'characters' | 'stats' | 'settings' | 'trophies' | 'account';

const MainMenu = ({ onStartGame, onCustomize, onAccount, highScore, selectedSprite, onSpriteSelect }: MainMenuProps) => {
  const [activePage, setActivePage] = useState<ActivePage>('main');
  const [coins] = useState(2850);
  const [level] = useState(15);
  const [streak] = useState(7);

  const renderPage = () => {
    switch (activePage) {
      case 'characters':
        return (
          <CharacterSelection
            selectedSprite={selectedSprite}
            onSpriteSelect={onSpriteSelect}
            onBack={() => setActivePage('main')}
          />
        );
      case 'stats':
        return <StatsPage onBack={() => setActivePage('main')} />;
      case 'settings':
        return <SettingsPage onBack={() => setActivePage('main')} />;
      case 'trophies':
        return <TrophiesPage onBack={() => setActivePage('main')} />;
      case 'account':
        return <PlayerAccount onBack={() => setActivePage('main')} />;
      default:
        return null;
    }
  };

  if (activePage !== 'main') {
    return renderPage();
  }

  return (
    <div className="h-screen w-full relative overflow-hidden flex flex-col">
      {/* Video Background - Preloaded for Zero Delay */}
      <div className="absolute inset-0 w-full h-full">
        <video
          autoPlay
          loop
          muted
          playsInline
          preload="auto"
          className="w-full h-full object-cover"
          style={{
            objectFit: 'cover',
            objectPosition: 'center center'
          }}
          onLoadStart={() => console.log('Video loading started')}
          onCanPlay={() => console.log('Video ready to play')}
          onError={(e) => console.error('Video error:', e)}
        >
          <source src="/lovable-uploads/scrollfitt.mp4" type="video/mp4" />
          {/* Fallback background if video fails */}
          <div
            className="w-full h-full"
            style={{ background: '#4A9EFF' }}
          />
        </video>
      </div>

      {/* Video Overlay for Better Text Readability */}
      <div
        className="absolute inset-0"
        style={{
          background: 'linear-gradient(to bottom, rgba(0,0,0,0.2) 0%, rgba(0,0,0,0.4) 100%)'
        }}
      />

      <div className="relative z-10 flex flex-col h-full">
        {/* Compact Header - Mobile Responsive */}
        <motion.div
          initial={{ opacity: 0, y: -50 }}
          animate={{ opacity: 1, y: 0 }}
          className="pt-4 sm:pt-6 px-3 sm:px-4 flex-shrink-0"
        >
          <div className="flex justify-between items-center mb-3 sm:mb-4">
            {/* Cartoon Logo - SCROLLFIT */}
            <motion.div
              initial={{ scale: 0.8 }}
              animate={{ scale: 1 }}
              transition={{ duration: 0.8, type: "spring", bounce: 0.4 }}
              className="relative"
            >
              <h1
                className="text-2xl sm:text-3xl md:text-4xl font-black text-white relative tracking-wider"
                style={{
                  textShadow: '-3px -3px 0 #000, 3px -3px 0 #000, -3px 3px 0 #000, 3px 3px 0 #000, -2px -2px 0 #000, 2px -2px 0 #000, -2px 2px 0 #000, 2px 2px 0 #000, -1px -1px 0 #000, 1px -1px 0 #000, -1px 1px 0 #000, 1px 1px 0 #000',
                  WebkitTextStroke: '2px #000'
                }}
              >
                SCROLLFIT
                <motion.div
                  className="absolute -top-1 -right-4 sm:-right-6 text-lg sm:text-xl"
                  animate={{
                    rotate: [0, 15, -15, 0],
                    scale: [1, 1.1, 1]
                  }}
                  transition={{ duration: 2, repeat: Infinity }}
                >
                  🏈
                </motion.div>
              </h1>
            </motion.div>

            {/* Compact Profile Section - Mobile Responsive */}
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              className="flex items-center space-x-1 sm:space-x-2"
            >
              {/* Account Button */}
              <motion.button
                whileHover={{ scale: 1.1, boxShadow: '0 0 25px rgba(57, 255, 20, 0.5)' }}
                whileTap={{ scale: 0.9 }}
                onClick={() => setActivePage('account')}
                className="w-8 h-8 sm:w-10 sm:h-10 rounded-full flex items-center justify-center border-2 border-[#39FF14] relative overflow-hidden"
                style={{
                  background: 'linear-gradient(135deg, rgba(57, 255, 20, 0.2), rgba(0, 212, 255, 0.2))',
                  backdropFilter: 'blur(20px)'
                }}
              >
                <UserCog className="w-4 h-4 sm:w-5 sm:h-5 text-[#39FF14]" />
              </motion.button>

              {/* Compact Coins Display */}
              <motion.div
                whileHover={{ scale: 1.05 }}
                className="flex items-center space-x-1 px-2 sm:px-3 py-1 sm:py-2 rounded-full border border-[#FFD700]/30 relative"
                style={{
                  background: 'linear-gradient(135deg, rgba(255, 215, 0, 0.1), rgba(255, 107, 53, 0.1))',
                  backdropFilter: 'blur(20px)'
                }}
              >
                <span className="text-lg sm:text-xl">🪙</span>
                <span className="text-[#FFD700] font-black text-xs sm:text-sm">{coins.toLocaleString()}</span>
              </motion.div>

              {/* Compact Profile Avatar */}
              <motion.div
                whileHover={{ scale: 1.1 }}
                className="relative w-10 h-10 sm:w-12 sm:h-12 rounded-full border-2 border-[#00D4FF] flex items-center justify-center overflow-hidden"
                style={{
                  background: 'linear-gradient(45deg, #00D4FF, #39FF14)',
                  boxShadow: '0 0 20px rgba(0, 212, 255, 0.4)'
                }}
              >
                <User className="w-5 h-5 sm:w-6 sm:h-6 text-black" />
                <motion.div
                  className="absolute -top-1 -right-1 w-5 h-5 sm:w-6 sm:h-6 bg-gradient-to-r from-[#FF6B35] to-[#F7931E] rounded-full flex items-center justify-center border-2 border-[#0A0B1A]"
                  animate={{ scale: [1, 1.1, 1] }}
                  transition={{ duration: 2, repeat: Infinity }}
                >
                  <span className="text-xs font-black text-white">{level}</span>
                </motion.div>
              </motion.div>
            </motion.div>
          </div>

          {/* Compact Welcome Section - Mobile Responsive */}
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.3 }}
            className="text-center mb-3 sm:mb-4 p-2 sm:p-3 rounded-xl border border-[#39FF14]/20"
            style={{
              background: 'linear-gradient(135deg, rgba(57, 255, 20, 0.05), rgba(0, 212, 255, 0.05))',
              backdropFilter: 'blur(15px)'
            }}
          >
            <h2 className="text-lg sm:text-xl font-black text-white mb-2">
              READY TO DOMINATE? 🔥
            </h2>
            <div className="flex justify-center items-center space-x-4 sm:space-x-6">
              <div className="text-center">
                <div className="text-[#39FF14] font-black text-base sm:text-lg">{streak} Days</div>
                <div className="text-[#B8BCC8] text-xs font-bold">STREAK 🚀</div>
              </div>
              <div className="text-center">
                <div className="text-[#00D4FF] font-black text-base sm:text-lg">Level {level}</div>
                <div className="text-[#B8BCC8] text-xs font-bold">CHAMPION ⚡</div>
              </div>
            </div>
          </motion.div>
        </motion.div>

        {/* Main Content - Mobile Responsive */}
        <div className="flex-1 px-3 sm:px-4 pb-3 sm:pb-4 flex flex-col justify-between">
          {/* Cartoon Play Button */}
          <motion.button
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4, type: "spring", bounce: 0.3 }}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={onStartGame}
            className="w-full p-6 rounded-xl font-black text-xl sm:text-2xl text-white mb-4 relative overflow-hidden cursor-pointer"
            style={{
              background: '#7CB342', // Bright green
              border: '4px solid #000000',
              boxShadow: '0 6px 0 #5A8A2F, 0 8px 20px rgba(0, 0, 0, 0.3)',
              textShadow: '-2px -2px 0 #000, 2px -2px 0 #000, -2px 2px 0 #000, 2px 2px 0 #000',
              transform: 'translateY(0)',
              transition: 'all 0.1s ease'
            }}
            onMouseDown={(e) => {
              e.currentTarget.style.transform = 'translateY(4px)';
              e.currentTarget.style.boxShadow = '0 2px 0 #5A8A2F, 0 4px 10px rgba(0, 0, 0, 0.3)';
            }}
            onMouseUp={(e) => {
              e.currentTarget.style.transform = 'translateY(0)';
              e.currentTarget.style.boxShadow = '0 6px 0 #5A8A2F, 0 8px 20px rgba(0, 0, 0, 0.3)';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.transform = 'translateY(0)';
              e.currentTarget.style.boxShadow = '0 6px 0 #5A8A2F, 0 8px 20px rgba(0, 0, 0, 0.3)';
            }}
          >
            <div className="flex items-center justify-center space-x-3">
              <motion.span
                animate={{ scale: [1, 1.1, 1] }}
                transition={{ duration: 1.5, repeat: Infinity }}
              >
                🎮
              </motion.span>
              <span>PLAY GAME</span>
              <motion.span
                animate={{
                  x: [0, 5, 0],
                  rotate: [0, 10, 0]
                }}
                transition={{ duration: 2, repeat: Infinity }}
              >
                ▶️
              </motion.span>
            </div>
          </motion.button>

          {/* Compact Gaming Cards Grid - Mobile Responsive */}
          <div className="grid grid-cols-2 gap-2 sm:gap-3 mb-3 sm:mb-4">
            {/* Players Card - Cartoon Style */}
            <motion.button
              initial={{ opacity: 0, x: -50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.5, type: "spring", bounce: 0.3 }}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => setActivePage('characters')}
              className="p-4 rounded-xl font-black text-white h-24 cursor-pointer"
              style={{
                background: '#4A9EFF', // Sky blue
                border: '3px solid #000000',
                boxShadow: '0 4px 0 #3A7BD5, 0 6px 15px rgba(0, 0, 0, 0.2)',
                textShadow: '-1px -1px 0 #000, 1px -1px 0 #000, -1px 1px 0 #000, 1px 1px 0 #000'
              }}
            >
              <div className="flex flex-col items-center justify-center h-full">
                <span className="text-2xl mb-1">👤</span>
                <span className="text-xs">PLAYERS</span>
              </div>
            </motion.button>

            {/* Trophies Card - Cartoon Style */}
            <motion.button
              initial={{ opacity: 0, x: 50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.6, type: "spring", bounce: 0.3 }}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => setActivePage('trophies')}
              className="p-4 rounded-xl font-black text-white h-24 cursor-pointer"
              style={{
                background: '#FFC107', // Golden yellow
                border: '3px solid #000000',
                boxShadow: '0 4px 0 #E6A100, 0 6px 15px rgba(0, 0, 0, 0.2)',
                textShadow: '-1px -1px 0 #000, 1px -1px 0 #000, -1px 1px 0 #000, 1px 1px 0 #000'
              }}
            >
              <div className="flex flex-col items-center justify-center h-full">
                <span className="text-2xl mb-1">🏆</span>
                <span className="text-xs">ACHIEVEMENTS</span>
              </div>
            </motion.button>

            {/* Stats Card - Cartoon Style */}
            <motion.button
              initial={{ opacity: 0, x: -50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.7, type: "spring", bounce: 0.3 }}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => setActivePage('stats')}
              className="p-4 rounded-xl font-black text-white h-24 cursor-pointer"
              style={{
                background: '#7CB342', // Bright green
                border: '3px solid #000000',
                boxShadow: '0 4px 0 #5A8A2F, 0 6px 15px rgba(0, 0, 0, 0.2)',
                textShadow: '-1px -1px 0 #000, 1px -1px 0 #000, -1px 1px 0 #000, 1px 1px 0 #000'
              }}
            >
              <div className="flex flex-col items-center justify-center h-full">
                <span className="text-2xl mb-1">📊</span>
                <span className="text-xs">STATS</span>
              </div>
            </motion.button>

            {/* Settings Card - Cartoon Style */}
            <motion.button
              initial={{ opacity: 0, x: 50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.8, type: "spring", bounce: 0.3 }}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => setActivePage('settings')}
              className="p-4 rounded-xl font-black text-white h-24 cursor-pointer"
              style={{
                background: '#F5F5F5', // Cloud gray
                border: '3px solid #000000',
                boxShadow: '0 4px 0 #D0D0D0, 0 6px 15px rgba(0, 0, 0, 0.2)',
                textShadow: '-1px -1px 0 #000, 1px -1px 0 #000, -1px 1px 0 #000, 1px 1px 0 #000',
                color: '#000000' // Black text for gray background
              }}
            >
              <div className="flex flex-col items-center justify-center h-full">
                <motion.span
                  className="text-2xl mb-1"
                  animate={{ rotate: 360 }}
                  transition={{ duration: 8, repeat: Infinity, ease: "linear" }}
                >
                  ⚙️
                </motion.span>
                <span className="text-xs">SETTINGS</span>
              </div>
            </motion.button>
          </div>

          {/* Compact High Score Display */}
          {highScore > 0 && (
            <motion.div
              initial={{ opacity: 0, y: 50 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.9 }}
              className="text-center"
            >
              <motion.div 
                whileHover={{ scale: 1.05 }}
                className="inline-flex items-center space-x-3 px-6 py-3 rounded-full border-2 border-[#FFD700]/30 relative"
                style={{
                  background: 'linear-gradient(135deg, rgba(255, 215, 0, 0.1), rgba(255, 107, 53, 0.1))',
                  backdropFilter: 'blur(25px)',
                  boxShadow: '0 15px 50px rgba(255, 215, 0, 0.3)'
                }}
              >
                <motion.div
                  animate={{ rotate: [0, 15, -15, 0] }}
                  transition={{ duration: 3, repeat: Infinity }}
                >
                  <Crown className="w-6 h-6 text-[#FFD700]" />
                </motion.div>
                <div>
                  <div className="text-[#FFD700] text-xs font-bold uppercase tracking-wider">
                    CHAMPION RECORD
                  </div>
                  <div 
                    className="text-xl font-black text-white"
                    style={{ textShadow: '0 0 25px #FFD700' }}
                  >
                    {highScore.toLocaleString()}
                  </div>
                </div>
              </motion.div>
            </motion.div>
          )}
        </div>
      </div>
    </div>
  );
};

export default MainMenu;
