
import React, { useEffect, useCallback, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import GameField from '../components/GameField';
import Player from '../components/Player';
import Collectible from '../components/Collectible';
import GameHUD from '../components/GameHUD';
import TouchControls from '../components/TouchControls';
import MainMenu from '../components/MainMenu';
import GameOverScreen from '../components/GameOverScreen';
import LoadingScreen from '../components/LoadingScreen';
import PlayerAccount from '../components/PlayerAccount';
import useOfflineGameState from '../hooks/useOfflineGameState';

const Index = () => {
  const {
    gameState,
    player,
    gameData,
    collectibles,
    highScore,
    selectedSprite,
    gameAssets,
    isNewHighScore,
    actions
  } = useOfflineGameState();

  const [currentPage, setCurrentPage] = useState<'main' | 'account'>('main');
  const [loadingComplete, setLoadingComplete] = useState(false);
  const [showLoadingScreen, setShowLoadingScreen] = useState(true);

  // Handle loading completion - this ensures 5-second minimum display
  const handleLoadingComplete = useCallback(() => {
    console.log('Game loading completed, assets ready for offline play');
    setLoadingComplete(true);
    // Hide loading screen after completion
    setShowLoadingScreen(false);
  }, []);

  // Enhanced game loop with better performance
  useEffect(() => {
    if (gameState !== 'playing') return;

    const gameLoop = setInterval(() => {
      // Move existing collectibles down and filter out those that are off-screen
      actions.setCollectibles(prev => {
        const moved = prev.map(item => ({
          ...item,
          y: item.y + (3 * gameData.speed) // Move collectibles down
        })).filter(item => item.y < 110 && !item.collected); // Keep items on screen

        // Enhanced spawn rate based on level and speed
        const spawnChance = 0.015 + (gameData.level * 0.003) + (gameData.speed * 0.005);
        if (Math.random() < spawnChance) {
          moved.push(actions.generateCollectible());
        }

        return moved;
      });
    }, 50); // 20fps update rate

    return () => clearInterval(gameLoop);
  }, [gameState, gameData.speed, gameData.level, actions]);

  // Enhanced collision detection with perfect lane alignment
  useEffect(() => {
    if (gameState !== 'playing') return;

    const checkCollisions = () => {
      collectibles.forEach(collectible => {
        if (collectible.collected) return;

        // Calculate lane based on exact positions
        const getLaneFromPosition = (x: number) => {
          if (x <= 25) return 0;      // Left lane
          if (x >= 75) return 2;      // Right lane
          return 1;                   // Center lane
        };

        const playerLane = player.lane;
        const collectibleLane = getLaneFromPosition(collectible.x);
        
        // Enhanced collision detection - check if collectible is in player area
        if (playerLane === collectibleLane && 
            collectible.y > 65 && collectible.y < 85) { // Player collision zone
          console.log('Collision detected!', collectible.type);
          actions.collectItem(collectible.id);
        }
      });
    };

    const collisionInterval = setInterval(checkCollisions, 16); // ~60fps collision checks
    return () => clearInterval(collisionInterval);
  }, [gameState, player.lane, collectibles, actions]);

  const handleCustomize = useCallback(() => {
    console.log('Opening character customization...');
  }, []);

  const handleAccountPage = useCallback(() => {
    setCurrentPage('account');
  }, []);

  const handleBackToMain = useCallback(() => {
    setCurrentPage('main');
  }, []);

  const getPlayerDirection = () => {
    if (player.lane === 0) return 'left';
    if (player.lane === 2) return 'right';
    return 'center';
  };

  // Enhanced keyboard controls for desktop testing
  useEffect(() => {
    const handleKeyPress = (event: KeyboardEvent) => {
      if (gameState !== 'playing') return;
      
      switch (event.key.toLowerCase()) {
        case 'arrowleft':
        case 'a':
          event.preventDefault();
          actions.moveLeft();
          break;
        case 'arrowright':
        case 'd':
          event.preventDefault();
          actions.moveRight();
          break;
        case ' ':
        case 'arrowup':
        case 'w':
          event.preventDefault();
          actions.jump();
          break;
        case 'p':
          event.preventDefault();
          if (gameState === 'playing') {
            actions.pauseGame();
          }
          break;
      }
    };

    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, [gameState, actions]);

  // Show loading screen for minimum 5 seconds or until assets are ready
  if (showLoadingScreen || gameState === 'loading' || !gameAssets.loaded) {
    return <LoadingScreen onLoadingComplete={handleLoadingComplete} />;
  }

  // Show account page
  if (currentPage === 'account') {
    return <PlayerAccount onBack={handleBackToMain} />;
  }

  return (
    <div className="w-full h-screen overflow-hidden relative" style={{
      background: '#4A9EFF' // Cartoon sky blue background
    }}>
      <AnimatePresence mode="wait">
        {gameState === 'menu' && (
          <MainMenu
            key="menu"
            onStartGame={actions.startGame}
            onCustomize={handleCustomize}
            onAccount={handleAccountPage}
            highScore={highScore}
            selectedSprite={selectedSprite}
            onSpriteSelect={actions.selectSprite}
          />
        )}

        {(gameState === 'playing' || gameState === 'paused') && (
          <motion.div
            key="game"
            initial={{ opacity: 0, scale: 0.98 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.98 }}
            className="w-full h-full relative"
          >
            {/* Premium NFL Stadium Field */}
            <GameField />

            {/* Enhanced Player with Proper Lane Movement */}
            <Player
              position={{ x: player.x, y: player.y }}
              isRunning={player.isRunning}
              direction={getPlayerDirection()}
              powerUp={gameData.combo > 5 ? 'fire' : null}
              selectedSprite={selectedSprite}
            />

            {/* Mental Wellness Collectibles with New Sprites */}
            <AnimatePresence>
              {collectibles.map((collectible) => (
                <Collectible
                  key={collectible.id}
                  type={collectible.type}
                  position={{ x: collectible.x, y: collectible.y }}
                  onCollect={() => actions.collectItem(collectible.id)}
                  isCollected={collectible.collected}
                />
              ))}
            </AnimatePresence>

            {/* Premium Glassmorphism HUD */}
            <GameHUD
              score={gameData.score}
              health={player.health}
              coins={gameData.coins}
              combo={gameData.combo}
              level={gameData.level}
            />

            {/* Enhanced Touch Controls */}
            <TouchControls
              onSwipeLeft={actions.moveLeft}
              onSwipeRight={actions.moveRight}
              onTap={actions.jump}
              gameState={gameState}
            />

            {/* Premium Pause Overlay */}
            {gameState === 'paused' && (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                className="absolute inset-0 backdrop-blur-xl flex items-center justify-center z-50"
                style={{
                  background: 'rgba(0, 0, 0, 0.7)'
                }}
              >
                <motion.div
                  initial={{ scale: 0, y: -100 }}
                  animate={{ scale: 1, y: 0 }}
                  className="rounded-3xl p-16 text-center border-2 border-white/30 shadow-2xl"
                  style={{
                    background: 'linear-gradient(135deg, rgba(59, 130, 246, 0.9) 0%, rgba(139, 92, 246, 0.9) 100%)',
                    backdropFilter: 'blur(25px)'
                  }}
                >
                  <motion.h2 
                    className="text-7xl font-black text-white mb-8 drop-shadow-xl"
                    animate={{ scale: [1, 1.05, 1] }}
                    transition={{ duration: 2, repeat: Infinity }}
                  >
                    ⏸️ PAUSED
                  </motion.h2>
                  <motion.button
                    whileHover={{ scale: 1.1, boxShadow: '0 15px 50px rgba(34, 197, 94, 0.4)' }}
                    whileTap={{ scale: 0.95 }}
                    onClick={actions.resumeGame}
                    className="rounded-full px-16 py-5 text-white font-black text-2xl shadow-2xl border-3 border-white/40"
                    style={{
                      background: 'linear-gradient(135deg, #10B981 0%, #3B82F6 100%)',
                      boxShadow: '0 12px 40px rgba(34, 197, 94, 0.4)'
                    }}
                  >
                    ▶️ RESUME GAME
                  </motion.button>
                </motion.div>
              </motion.div>
            )}
          </motion.div>
        )}

        {gameState === 'gameOver' && (
          <GameOverScreen
            key="gameOver"
            score={gameData.score}
            highScore={highScore}
            coins={gameData.coins}
            onRestart={actions.startGame}
            onMainMenu={actions.returnToMenu}
            isNewHighScore={isNewHighScore}
          />
        )}
      </AnimatePresence>
    </div>
  );
};

export default Index;
