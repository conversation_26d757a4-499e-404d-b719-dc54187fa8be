
import React from 'react';
import { motion } from 'framer-motion';
import { ScrollArea } from '@/components/ui/scroll-area';
import { ArrowLeft, TrendingUp, Target, Zap, Brain, Calendar, Trophy, Clock } from 'lucide-react';

interface StatsPageProps {
  onBack: () => void;
}

const StatsPage = ({ onBack }: StatsPageProps) => {
  const stats = {
    totalGames: 156,
    totalScore: 98750,
    averageScore: 633,
    bestStreak: 12,
    totalTime: '24h 32m',
    mentalWellness: 85,
    collectiblesGathered: 2847,
    distanceRun: '15.2 km'
  };

  const weeklyProgress = [
    { day: 'Mon', score: 420, wellness: 78 },
    { day: 'Tue', score: 650, wellness: 82 },
    { day: 'Wed', score: 890, wellness: 85 },
    { day: 'Thu', score: 720, wellness: 88 },
    { day: 'Fri', score: 980, wellness: 90 },
    { day: 'Sat', score: 1100, wellness: 92 },
    { day: 'Sun', score: 850, wellness: 89 }
  ];

  const achievements = [
    { title: 'First Steps', description: 'Complete your first game', completed: true },
    { title: 'Speed Demon', description: 'Score 1000+ points in a single game', completed: true },
    { title: 'Wellness Warrior', description: 'Maintain 90% wellness for a week', completed: false, progress: 71 },
    { title: 'Marathon Runner', description: 'Play for 30 days straight', completed: false, progress: 87 }
  ];

  return (
    <div className="min-h-screen w-full relative overflow-hidden" style={{
      background: 'linear-gradient(135deg, #0A0B1A 0%, #161B33 50%, #0D1117 100%)'
    }}>
      {/* Animated Background */}
      <div className="absolute inset-0 overflow-hidden">
        {[...Array(30)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-2 h-2 bg-white/10 rounded-full"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
            animate={{
              opacity: [0.1, 0.6, 0.1],
              scale: [1, 2, 1],
            }}
            transition={{
              duration: 3 + Math.random() * 2,
              repeat: Infinity,
              delay: Math.random() * 2,
            }}
          />
        ))}
      </div>

      <ScrollArea className="h-screen w-full">
        <div className="relative z-10 px-6 pb-8">
          {/* Header */}
          <motion.div
            initial={{ opacity: 0, y: -50 }}
            animate={{ opacity: 1, y: 0 }}
            className="pt-12 mb-8"
          >
            <div className="flex items-center justify-between mb-6">
              <motion.button
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                onClick={onBack}
                className="w-12 h-12 rounded-full flex items-center justify-center border border-white/20"
                style={{
                  background: 'rgba(255, 255, 255, 0.1)',
                  backdropFilter: 'blur(20px)'
                }}
              >
                <ArrowLeft className="w-6 h-6 text-white" />
              </motion.button>

              <div className="text-center">
                <h1 
                  className="text-3xl font-black text-white mb-2"
                  style={{ textShadow: '0 0 20px #00FF7F' }}
                >
                  PERFORMANCE STATS
                </h1>
                <p className="text-[#B8BCC8]">Track your mental fitness journey</p>
              </div>

              <div className="w-12" />
            </div>
          </motion.div>

          {/* Stats Overview */}
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.2 }}
            className="grid grid-cols-2 gap-4 mb-8"
          >
            {/* Total Games */}
            <div 
              className="p-4 rounded-2xl border border-white/10"
              style={{
                background: 'linear-gradient(145deg, rgba(0, 212, 255, 0.1), rgba(74, 158, 255, 0.1))',
                backdropFilter: 'blur(15px)'
              }}
            >
              <div className="flex items-center justify-between mb-2">
                <Trophy className="w-6 h-6 text-[#00D4FF]" />
                <span className="text-2xl font-black text-white">{stats.totalGames}</span>
              </div>
              <p className="text-[#B8BCC8] text-sm">Games Played</p>
            </div>

            {/* Best Score */}
            <div 
              className="p-4 rounded-2xl border border-white/10"
              style={{
                background: 'linear-gradient(145deg, rgba(57, 255, 20, 0.1), rgba(0, 255, 136, 0.1))',
                backdropFilter: 'blur(15px)'
              }}
            >
              <div className="flex items-center justify-between mb-2">
                <Target className="w-6 h-6 text-[#39FF14]" />
                <span className="text-2xl font-black text-white">{stats.totalScore.toLocaleString()}</span>
              </div>
              <p className="text-[#B8BCC8] text-sm">Total Score</p>
            </div>

            {/* Mental Wellness */}
            <div 
              className="p-4 rounded-2xl border border-white/10"
              style={{
                background: 'linear-gradient(145deg, rgba(139, 92, 246, 0.1), rgba(168, 85, 247, 0.1))',
                backdropFilter: 'blur(15px)'
              }}
            >
              <div className="flex items-center justify-between mb-2">
                <Brain className="w-6 h-6 text-[#8B5CF6]" />
                <span className="text-2xl font-black text-white">{stats.mentalWellness}%</span>
              </div>
              <p className="text-[#B8BCC8] text-sm">Mental Wellness</p>
            </div>

            {/* Play Time */}
            <div 
              className="p-4 rounded-2xl border border-white/10"
              style={{
                background: 'linear-gradient(145deg, rgba(255, 107, 53, 0.1), rgba(255, 140, 66, 0.1))',
                backdropFilter: 'blur(15px)'
              }}
            >
              <div className="flex items-center justify-between mb-2">
                <Clock className="w-6 h-6 text-[#FF6B35]" />
                <span className="text-2xl font-black text-white">{stats.totalTime}</span>
              </div>
              <p className="text-[#B8BCC8] text-sm">Total Play Time</p>
            </div>
          </motion.div>

          {/* Weekly Progress Chart */}
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
            className="mb-8"
          >
            <div 
              className="p-6 rounded-2xl border border-white/10"
              style={{
                background: 'rgba(255, 255, 255, 0.05)',
                backdropFilter: 'blur(20px)',
                boxShadow: '0 8px 32px rgba(0, 0, 0, 0.3)'
              }}
            >
              <div className="flex items-center space-x-2 mb-6">
                <TrendingUp className="w-6 h-6 text-[#39FF14]" />
                <h3 className="text-xl font-bold text-white">Weekly Progress</h3>
              </div>
              
              <div className="space-y-4">
                {weeklyProgress.map((day, index) => (
                  <motion.div
                    key={day.day}
                    initial={{ x: -50, opacity: 0 }}
                    animate={{ x: 0, opacity: 1 }}
                    transition={{ delay: 0.5 + index * 0.1 }}
                    className="flex items-center justify-between"
                  >
                    <div className="flex items-center space-x-4 flex-1">
                      <span className="text-white font-medium w-8">{day.day}</span>
                      
                      {/* Score Bar */}
                      <div className="flex-1 h-3 bg-white/10 rounded-full overflow-hidden">
                        <motion.div
                          className="h-full rounded-full"
                          style={{
                            background: 'linear-gradient(90deg, #39FF14, #00FF88)',
                            width: `${(day.score / 1200) * 100}%`
                          }}
                          initial={{ width: 0 }}
                          animate={{ width: `${(day.score / 1200) * 100}%` }}
                          transition={{ delay: 0.5 + index * 0.1, duration: 0.5 }}
                        />
                      </div>
                      
                      <span className="text-[#39FF14] font-bold w-12 text-right">{day.score}</span>
                    </div>
                    
                    {/* Wellness Indicator */}
                    <div className="ml-4 flex items-center space-x-2">
                      <div 
                        className="w-3 h-3 rounded-full"
                        style={{
                          backgroundColor: day.wellness > 85 ? '#39FF14' : day.wellness > 70 ? '#FFD700' : '#FF6B35'
                        }}
                      />
                      <span className="text-[#B8BCC8] text-sm w-8">{day.wellness}%</span>
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>
          </motion.div>

          {/* Achievement Progress */}
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6 }}
          >
            <div 
              className="p-6 rounded-2xl border border-white/10"
              style={{
                background: 'rgba(255, 255, 255, 0.05)',
                backdropFilter: 'blur(20px)',
                boxShadow: '0 8px 32px rgba(0, 0, 0, 0.3)'
              }}
            >
              <div className="flex items-center space-x-2 mb-6">
                <Trophy className="w-6 h-6 text-[#FFD700]" />
                <h3 className="text-xl font-bold text-white">Recent Achievements</h3>
              </div>
              
              <div className="space-y-4">
                {achievements.map((achievement, index) => (
                  <motion.div
                    key={achievement.title}
                    initial={{ x: -50, opacity: 0 }}
                    animate={{ x: 0, opacity: 1 }}
                    transition={{ delay: 0.7 + index * 0.1 }}
                    className="flex items-center space-x-4"
                  >
                    {/* Status Icon */}
                    <div 
                      className={`w-8 h-8 rounded-full flex items-center justify-center ${
                        achievement.completed 
                          ? 'bg-[#39FF14] text-black' 
                          : 'bg-white/10 text-[#B8BCC8]'
                      }`}
                    >
                      {achievement.completed ? '✓' : '○'}
                    </div>
                    
                    {/* Achievement Info */}
                    <div className="flex-1">
                      <div className="flex items-center justify-between mb-1">
                        <h4 className="text-white font-medium">{achievement.title}</h4>
                        {!achievement.completed && achievement.progress && (
                          <span className="text-[#00D4FF] text-sm font-bold">{achievement.progress}%</span>
                        )}
                      </div>
                      <p className="text-[#B8BCC8] text-sm">{achievement.description}</p>
                      
                      {/* Progress Bar for incomplete achievements */}
                      {!achievement.completed && achievement.progress && (
                        <div className="mt-2 h-2 bg-white/10 rounded-full overflow-hidden">
                          <motion.div
                            className="h-full bg-[#00D4FF] rounded-full"
                            initial={{ width: 0 }}
                            animate={{ width: `${achievement.progress}%` }}
                            transition={{ delay: 0.8 + index * 0.1, duration: 0.5 }}
                          />
                        </div>
                      )}
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>
          </motion.div>
        </div>
      </ScrollArea>
    </div>
  );
};

export default StatsPage;
