
import React from 'react';
import { motion } from 'framer-motion';

interface TouchControlsProps {
  onSwipeLeft: () => void;
  onSwipeRight: () => void;
  onTap: () => void;
  gameState: 'menu' | 'playing' | 'paused' | 'gameOver';
}

const TouchControls = ({ onSwipeLeft, onSwipeRight, onTap, gameState }: TouchControlsProps) => {
  const [touchStart, setTouchStart] = React.useState<number | null>(null);
  const [touchEnd, setTouchEnd] = React.useState<number | null>(null);

  const minSwipeDistance = 50;

  const handleTouchStart = (e: React.TouchEvent) => {
    setTouchEnd(null);
    setTouchStart(e.targetTouches[0].clientX);
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    setTouchEnd(e.targetTouches[0].clientX);
  };

  const handleTouchEnd = () => {
    if (!touchStart || !touchEnd) return;
    
    const distance = touchStart - touchEnd;
    const isLeftSwipe = distance > minSwipeDistance;
    const isRightSwipe = distance < -minSwipeDistance;

    if (isLeftSwipe) {
      onSwipeLeft();
    } else if (isRightSwipe) {
      onSwipeRight();
    } else {
      onTap();
    }
  };

  if (gameState === 'playing') {
    return (
      <div
        className="absolute inset-0 z-10"
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
        style={{ touchAction: 'none' }}
      >
        {/* Invisible touch area for gameplay */}
      </div>
    );
  }

  return (
    <div className="absolute bottom-4 sm:bottom-8 left-0 right-0 z-30 flex justify-center px-4">
      <motion.div
        initial={{ y: 100, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        className="bg-black/20 backdrop-blur-md rounded-2xl sm:rounded-3xl p-4 sm:p-6 md:p-8 shadow-2xl border border-white/20 max-w-sm sm:max-w-md"
        style={{
          background: 'linear-gradient(135deg, rgba(0,0,0,0.3) 0%, rgba(0,0,0,0.1) 100%)',
          boxShadow: '0 20px 40px rgba(0,0,0,0.3), inset 0 1px 0 rgba(255,255,255,0.1)'
        }}
      >
        <div className="flex items-center space-x-4 sm:space-x-6 md:space-x-8">
          {/* Left Arrow */}
          <motion.button
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
            onClick={onSwipeLeft}
            className="w-14 h-14 sm:w-16 sm:h-16 md:w-18 md:h-18 rounded-full flex items-center justify-center text-2xl sm:text-3xl shadow-lg border-2 border-white/30"
            style={{
              background: 'linear-gradient(135deg, #3B82F6 0%, #1D4ED8 100%)',
              boxShadow: '0 8px 32px rgba(59, 130, 246, 0.4), inset 0 1px 0 rgba(255, 255, 255, 0.2)'
            }}
          >
            <motion.span
              className="text-white drop-shadow-lg"
              animate={{ x: [-2, 0, -2] }}
              transition={{ duration: 1.5, repeat: Infinity }}
            >
              ⬅️
            </motion.span>
          </motion.button>

          {/* Center Action Button */}
          <motion.button
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
            onClick={onTap}
            className="w-18 h-18 sm:w-20 sm:h-20 md:w-24 md:h-24 rounded-full flex items-center justify-center text-3xl sm:text-4xl shadow-lg border-2 border-white/30"
            style={{
              background: 'linear-gradient(135deg, #10B981 0%, #059669 100%)',
              boxShadow: '0 8px 32px rgba(16, 185, 129, 0.4), inset 0 1px 0 rgba(255, 255, 255, 0.2)'
            }}
            animate={{
              boxShadow: [
                '0 8px 32px rgba(16, 185, 129, 0.4)',
                '0 12px 40px rgba(16, 185, 129, 0.6)',
                '0 8px 32px rgba(16, 185, 129, 0.4)'
              ]
            }}
            transition={{ duration: 2, repeat: Infinity }}
          >
            <motion.span
              className="text-white drop-shadow-lg"
              animate={{
                scale: [1, 1.1, 1],
                y: [0, -2, 0]
              }}
              transition={{ duration: 1, repeat: Infinity }}
            >
              🏃‍♂️
            </motion.span>
          </motion.button>

          {/* Right Arrow */}
          <motion.button
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
            onClick={onSwipeRight}
            className="w-14 h-14 sm:w-16 sm:h-16 md:w-18 md:h-18 rounded-full flex items-center justify-center text-2xl sm:text-3xl shadow-lg border-2 border-white/30"
            style={{
              background: 'linear-gradient(135deg, #3B82F6 0%, #1D4ED8 100%)',
              boxShadow: '0 8px 32px rgba(59, 130, 246, 0.4), inset 0 1px 0 rgba(255, 255, 255, 0.2)'
            }}
          >
            <motion.span
              className="text-white drop-shadow-lg"
              animate={{ x: [2, 0, 2] }}
              transition={{ duration: 1.5, repeat: Infinity }}
            >
              ➡️
            </motion.span>
          </motion.button>
        </div>

        {/* Enhanced Instruction Text - Mobile Responsive */}
        <motion.div
          className="text-center mt-3 sm:mt-4 md:mt-6"
          animate={{ opacity: [0.7, 1, 0.7] }}
          transition={{ duration: 2, repeat: Infinity }}
        >
          <p className="text-white text-xs sm:text-sm font-semibold tracking-wide drop-shadow-lg">
            Swipe to dodge • Tap to sprint
          </p>
          <div className="flex justify-center mt-1 sm:mt-2 space-x-2 sm:space-x-4">
            <div className="w-6 sm:w-8 h-1 bg-white/40 rounded-full"></div>
            <div className="w-6 sm:w-8 h-1 bg-white/60 rounded-full"></div>
            <div className="w-6 sm:w-8 h-1 bg-white/40 rounded-full"></div>
          </div>
        </motion.div>

        {/* Gesture Hints - Mobile Responsive */}
        <div className="absolute -top-3 sm:-top-4 left-1/2 transform -translate-x-1/2">
          <motion.div
            animate={{ y: [0, -5, 0] }}
            transition={{ duration: 1.5, repeat: Infinity }}
            className="bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full px-3 sm:px-4 py-1 sm:py-2 shadow-lg border border-white/30"
          >
            <span className="text-white text-xs font-bold tracking-wide drop-shadow-sm">
              ✨ TAP TO PLAY
            </span>
          </motion.div>
        </div>
      </motion.div>
    </div>
  );
};

export default TouchControls;
