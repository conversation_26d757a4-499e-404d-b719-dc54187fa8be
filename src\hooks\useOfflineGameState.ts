
import { useState, useCallback, useEffect } from 'react';

export type GameState = 'loading' | 'menu' | 'playing' | 'paused' | 'gameOver';

interface Player {
  x: number;
  y: number;
  lane: number; // 0, 1, 2 for left, center, right
  health: number;
  isRunning: boolean;
}

interface Collectible {
  id: string;
  type: 'apple' | 'banana' | 'carrot' | 'broccoli' | 'watermelon' | 'eggplant' | 'anxiety' | 'worry' | 'failure' | 'obstacle';
  x: number;
  y: number;
  collected: boolean;
  isPositive: boolean;
}

interface GameData {
  score: number;
  coins: number;
  combo: number;
  level: number;
  speed: number;
}

interface GameAssets {
  sprites: { [key: string]: string };
  sounds: { [key: string]: string };
  loaded: boolean;
}

const useOfflineGameState = () => {
  const [gameState, setGameState] = useState<GameState>('loading');
  const [selectedSprite, setSelectedSprite] = useState(0);
  const [gameAssets, setGameAssets] = useState<GameAssets>({
    sprites: {},
    sounds: {},
    loaded: false
  });
  
  const [player, setPlayer] = useState<Player>({
    x: 50, // Center position
    y: 20, // Bottom area
    lane: 1, // Center lane (0=left, 1=center, 2=right)
    health: 3,
    isRunning: false
  });
  
  const [gameData, setGameData] = useState<GameData>({
    score: 0,
    coins: 0,
    combo: 0,
    level: 1,
    speed: 1
  });

  const [collectibles, setCollectibles] = useState<Collectible[]>([]);
  const [highScore, setHighScore] = useState(0);

  // Improved lane positions to prevent cutoff and ensure proper proportions
  const getLanePosition = (lane: number) => {
    switch (lane) {
      case 0: return 25;   // Left lane (25% from left edge - safe margin)
      case 1: return 50;   // Center lane (exactly centered)
      case 2: return 75;   // Right lane (75% from left edge - safe margin)
      default: return 50;
    }
  };

  // Load game assets for offline play
  const loadGameAssets = useCallback(async () => {
    try {
      // Preload all sprites and assets
      const spriteUrls = {
        // Player sprites
        player0: '/lovable-uploads/de160ff6-207c-40ea-a340-dd26f1824f75.png',
        player1: '/lovable-uploads/e74579c3-0c3b-42b2-8fbb-4ea923e0d4ac.png',
        player2: '/lovable-uploads/4c73eaf9-d066-4572-a35a-9892d2556bb5.png',
        player3: '/lovable-uploads/9cbe91da-dc2b-472f-a079-a7256b30e52a.png',
        player4: '/lovable-uploads/f35d2efa-b3f9-46f7-b3ac-03293ae08729.png',
        player5: '/lovable-uploads/1625f81a-abce-40ef-a214-dacdc5a60388.png',
        player6: '/lovable-uploads/c27ceaa2-cfdb-472b-944e-7c09b313a302.png',
        player7: '/lovable-uploads/ede3c27f-0d43-4dd3-9048-9d90e5b87669.png',
        player8: '/lovable-uploads/780a52af-4f53-4651-9169-cdfe7b8025bf.png',
        player9: '/lovable-uploads/7ba0c46b-0c26-4d7f-a2b4-4bb231a1adf6.png',
        
        // Collectible sprites (from provided images)
        apple: '/lovable-uploads/cb05c85e-4346-4745-838a-c3cf25481ed2.png', // Will be extracted
        anxiety: '/lovable-uploads/0d2b98c2-ad09-43fb-afc2-d99144468bf2.png', // Will be extracted
        vegetables: '/lovable-uploads/5204899d-7cf1-4243-b145-5998c08ab909.png', // Will be extracted
        obstacles: '/lovable-uploads/a179c2e2-b8a4-4cbe-ac2a-d74c24168e6d.png' // Will be extracted
      };

      // Cache all sprites in memory
      const loadPromises = Object.entries(spriteUrls).map(async ([key, url]) => {
        const img = new Image();
        img.src = url;
        await new Promise((resolve, reject) => {
          img.onload = resolve;
          img.onerror = reject;
        });
        return [key, url];
      });

      const loadedSprites = await Promise.all(loadPromises);
      const spritesMap = Object.fromEntries(loadedSprites);

      setGameAssets({
        sprites: spritesMap,
        sounds: {}, // Add sound loading here if needed
        loaded: true
      });

      return true;
    } catch (error) {
      console.error('Failed to load game assets:', error);
      return false;
    }
  }, []);

  // Initialize offline game
  useEffect(() => {
    const initGame = async () => {
      console.log('Initializing offline game...');
      
      // Load high score from localStorage
      const savedHighScore = localStorage.getItem('scrollfit-highscore');
      if (savedHighScore) {
        setHighScore(parseInt(savedHighScore));
      }

      // Load game assets
      const assetsLoaded = await loadGameAssets();
      
      if (assetsLoaded) {
        console.log('All assets loaded successfully');
        setGameState('menu');
      }
    };

    if (gameState === 'loading') {
      initGame();
    }
  }, [gameState, loadGameAssets]);

  // Save high score
  useEffect(() => {
    if (gameData.score > highScore) {
      setHighScore(gameData.score);
      localStorage.setItem('scrollfit-highscore', gameData.score.toString());
    }
  }, [gameData.score, highScore]);

  const moveLeft = useCallback(() => {
    if (gameState !== 'playing') return;
    
    setPlayer(prev => {
      const newLane = Math.max(0, prev.lane - 1);
      return {
        ...prev,
        lane: newLane,
        x: getLanePosition(newLane)
      };
    });
  }, [gameState]);

  const moveRight = useCallback(() => {
    if (gameState !== 'playing') return;
    
    setPlayer(prev => {
      const newLane = Math.min(2, prev.lane + 1);
      return {
        ...prev,
        lane: newLane,
        x: getLanePosition(newLane)
      };
    });
  }, [gameState]);

  const jump = useCallback(() => {
    if (gameState !== 'playing') return;
    
    setPlayer(prev => ({ ...prev, isRunning: !prev.isRunning }));
  }, [gameState]);

  const startGame = useCallback(() => {
    setGameState('playing');
    setPlayer({
      x: getLanePosition(1), // Start in center lane with proper positioning
      y: 20,
      lane: 1,
      health: 3,
      isRunning: true
    });
    setGameData({
      score: 0,
      coins: 0,
      combo: 0,
      level: 1,
      speed: 1
    });
    setCollectibles([]);
  }, []);

  const pauseGame = useCallback(() => {
    setGameState('paused');
    setPlayer(prev => ({ ...prev, isRunning: false }));
  }, []);

  const resumeGame = useCallback(() => {
    setGameState('playing');
    setPlayer(prev => ({ ...prev, isRunning: true }));
  }, []);

  const endGame = useCallback(() => {
    setGameState('gameOver');
    setPlayer(prev => ({ ...prev, isRunning: false }));
  }, []);

  const returnToMenu = useCallback(() => {
    setGameState('menu');
    setPlayer(prev => ({ ...prev, isRunning: false }));
  }, []);

  const selectSprite = useCallback((spriteId: number) => {
    const sprites = [
      { id: 0, unlocked: true },
      { id: 1, unlocked: true },
      { id: 2, unlocked: true },
      { id: 3, unlocked: true },
      { id: 4, unlocked: false },
      { id: 5, unlocked: false },
      { id: 6, unlocked: false },
      { id: 7, unlocked: false },
      { id: 8, unlocked: false },
      { id: 9, unlocked: false }
    ];
    
    const sprite = sprites.find(s => s.id === spriteId);
    if (sprite && sprite.unlocked) {
      setSelectedSprite(spriteId);
      console.log(`Selected character: ${spriteId}`);
    }
  }, []);

  const generateCollectible = useCallback((): Collectible => {
    // Mix of positive and negative collectibles based on provided sprites
    const positiveTypes: Array<Collectible['type']> = [
      'apple', 'banana', 'carrot', 'broccoli', 'watermelon', 'eggplant'
    ];
    
    const negativeTypes: Array<Collectible['type']> = [
      'anxiety', 'worry', 'failure', 'obstacle'
    ];
    
    // 70% chance for positive, 30% for negative
    const isPositive = Math.random() > 0.3;
    const types = isPositive ? positiveTypes : negativeTypes;
    const type = types[Math.floor(Math.random() * types.length)];
    
    return {
      id: Math.random().toString(36).substr(2, 9),
      type: type,
      x: getLanePosition(Math.floor(Math.random() * 3)), // Use improved lane positioning
      y: -10, // Start above screen
      collected: false,
      isPositive
    };
  }, []);

  const collectItem = useCallback((id: string) => {
    const collectible = collectibles.find(item => item.id === id);
    if (!collectible || collectible.collected) return;

    console.log('Collecting item:', collectible.type);

    // Mark item as collected
    setCollectibles(prev => prev.map(item => 
      item.id === id ? { ...item, collected: true } : item
    ));

    // Update game data based on collectible type
    setGameData(prev => {
      let pointsGained = 0;
      let coinsGained = 0;
      let newCombo = prev.combo;

      if (collectible.isPositive) {
        // Positive collectibles
        switch (collectible.type) {
          case 'apple':
            pointsGained = 15;
            coinsGained = 2;
            newCombo = prev.combo + 1;
            break;
          case 'banana':
            pointsGained = 20;
            coinsGained = 3;
            newCombo = prev.combo + 1;
            break;
          case 'carrot':
            pointsGained = 25;
            coinsGained = 3;
            newCombo = prev.combo + 1;
            break;
          case 'broccoli':
            pointsGained = 30;
            coinsGained = 4;
            newCombo = prev.combo + 1;
            break;
          case 'watermelon':
            pointsGained = 35;
            coinsGained = 5;
            newCombo = prev.combo + 1;
            break;
          case 'eggplant':
            pointsGained = 40;
            coinsGained = 6;
            newCombo = prev.combo + 1;
            break;
        }
      } else {
        // Negative collectibles
        pointsGained = -15;
        newCombo = 0;
        // Damage player
        setPlayer(p => {
          const newHealth = Math.max(0, p.health - 1);
          if (newHealth === 0) {
            setTimeout(endGame, 500);
          }
          return { ...p, health: newHealth };
        });
      }

      const multiplier = Math.max(1, Math.floor(newCombo / 5));
      const finalScore = prev.score + (pointsGained * multiplier);

      return {
        ...prev,
        score: Math.max(0, finalScore),
        coins: prev.coins + coinsGained,
        combo: newCombo,
        level: Math.floor(finalScore / 500) + 1,
        speed: 1 + (Math.floor(finalScore / 500) * 0.2)
      };
    });

    // Remove collected item after animation
    setTimeout(() => {
      setCollectibles(prev => prev.filter(item => item.id !== id));
    }, 1000);

  }, [collectibles, endGame]);

  return {
    gameState,
    player,
    gameData,
    collectibles,
    highScore,
    selectedSprite,
    gameAssets,
    isNewHighScore: gameData.score > highScore && gameData.score > 0,
    actions: {
      moveLeft,
      moveRight,
      jump,
      startGame,
      pauseGame,
      resumeGame,
      endGame,
      returnToMenu,
      generateCollectible,
      collectItem,
      setCollectibles,
      selectSprite,
      setGameState
    }
  };
};

export default useOfflineGameState;
