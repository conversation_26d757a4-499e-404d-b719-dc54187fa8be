import { useState, useCallback, useEffect } from 'react';

export type GameState = 'menu' | 'playing' | 'paused' | 'gameOver';

interface Player {
  x: number;
  y: number;
  lane: number; // 0, 1, 2 for left, center, right
  health: number;
  isRunning: boolean;
}

interface Collectible {
  id: string;
  type: 'apple' | 'banana' | 'strawberry' | 'avocado' | 'happy' | 'sad' | 'strong';
  x: number;
  y: number;
  collected: boolean;
}

interface GameData {
  score: number;
  coins: number;
  combo: number;
  level: number;
  speed: number;
}

const useGameState = () => {
  const [gameState, setGameState] = useState<GameState>('menu');
  const [selectedSprite, setSelectedSprite] = useState(0); // Default to first unlocked sprite
  
  const [player, setPlayer] = useState<Player>({
    x: 50, // Center position
    y: 20, // Bottom area
    lane: 1, // Center lane
    health: 3,
    isRunning: false
  });
  
  const [gameData, setGameData] = useState<GameData>({
    score: 0,
    coins: 0,
    combo: 0,
    level: 1,
    speed: 1
  });

  const [collectibles, setCollectibles] = useState<Collectible[]>([]);
  const [highScore, setHighScore] = useState(0);

  // Load high score from localStorage
  useEffect(() => {
    const savedHighScore = localStorage.getItem('scrollfit-highscore');
    if (savedHighScore) {
      setHighScore(parseInt(savedHighScore));
    }
  }, []);

  // Save high score
  useEffect(() => {
    if (gameData.score > highScore) {
      setHighScore(gameData.score);
      localStorage.setItem('scrollfit-highscore', gameData.score.toString());
    }
  }, [gameData.score, highScore]);

  const getLanePosition = (lane: number) => {
    switch (lane) {
      case 0: return 25; // Left lane
      case 1: return 50; // Center lane
      case 2: return 75; // Right lane
      default: return 50;
    }
  };

  const moveLeft = useCallback(() => {
    if (gameState !== 'playing') return;
    
    setPlayer(prev => {
      const newLane = Math.max(0, prev.lane - 1);
      return {
        ...prev,
        lane: newLane,
        x: getLanePosition(newLane)
      };
    });
  }, [gameState]);

  const moveRight = useCallback(() => {
    if (gameState !== 'playing') return;
    
    setPlayer(prev => {
      const newLane = Math.min(2, prev.lane + 1);
      return {
        ...prev,
        lane: newLane,
        x: getLanePosition(newLane)
      };
    });
  }, [gameState]);

  const jump = useCallback(() => {
    if (gameState !== 'playing') return;
    
    setPlayer(prev => ({ ...prev, isRunning: !prev.isRunning }));
  }, [gameState]);

  const startGame = useCallback(() => {
    setGameState('playing');
    setPlayer({
      x: 50,
      y: 20,
      lane: 1,
      health: 3,
      isRunning: true
    });
    setGameData({
      score: 0,
      coins: 0,
      combo: 0,
      level: 1,
      speed: 1
    });
    setCollectibles([]);
  }, []);

  const pauseGame = useCallback(() => {
    setGameState('paused');
    setPlayer(prev => ({ ...prev, isRunning: false }));
  }, []);

  const resumeGame = useCallback(() => {
    setGameState('playing');
    setPlayer(prev => ({ ...prev, isRunning: true }));
  }, []);

  const endGame = useCallback(() => {
    setGameState('gameOver');
    setPlayer(prev => ({ ...prev, isRunning: false }));
  }, []);

  const returnToMenu = useCallback(() => {
    setGameState('menu');
    setPlayer(prev => ({ ...prev, isRunning: false }));
  }, []);

  const selectSprite = useCallback((spriteId: number) => {
    const sprites = [
      { id: 0, unlocked: true },
      { id: 1, unlocked: true },
      { id: 2, unlocked: true },
      { id: 3, unlocked: true },
      { id: 4, unlocked: false },
      { id: 5, unlocked: false },
      { id: 6, unlocked: false },
      { id: 7, unlocked: false },
      { id: 8, unlocked: false },
      { id: 9, unlocked: false }
    ];
    
    const sprite = sprites.find(s => s.id === spriteId);
    if (sprite && sprite.unlocked) {
      setSelectedSprite(spriteId);
      console.log(`Selected character: ${spriteId}`);
    }
  }, []);

  const generateCollectible = useCallback((): Collectible => {
    const types: Collectible['type'][] = [
      'apple', 'banana', 'strawberry', 'avocado', 
      'happy', 'sad', 'strong'
    ];
    
    return {
      id: Math.random().toString(36).substr(2, 9),
      type: types[Math.floor(Math.random() * types.length)],
      x: getLanePosition(Math.floor(Math.random() * 3)),
      y: -10, // Start above screen
      collected: false
    };
  }, []);

  const collectItem = useCallback((id: string) => {
    const collectible = collectibles.find(item => item.id === id);
    if (!collectible || collectible.collected) return;

    console.log('Collecting item:', collectible.type);

    // Mark item as collected
    setCollectibles(prev => prev.map(item => 
      item.id === id ? { ...item, collected: true } : item
    ));

    // Update game data based on collectible type
    setGameData(prev => {
      let pointsGained = 0;
      let coinsGained = 0;
      let newCombo = prev.combo;

      switch (collectible.type) {
        case 'apple':
          pointsGained = 10;
          coinsGained = 1;
          newCombo = prev.combo + 1;
          break;
        case 'banana':
          pointsGained = 15;
          coinsGained = 2;
          newCombo = prev.combo + 1;
          break;
        case 'strawberry':
          pointsGained = 20;
          coinsGained = 3;
          newCombo = prev.combo + 1;
          break;
        case 'avocado':
          pointsGained = 25;
          coinsGained = 4;
          newCombo = prev.combo + 1;
          break;
        case 'happy':
          pointsGained = 30;
          coinsGained = 5;
          newCombo = prev.combo + 1;
          break;
        case 'sad':
          pointsGained = -10;
          newCombo = 0;
          // Damage player
          setPlayer(p => {
            const newHealth = Math.max(0, p.health - 1);
            if (newHealth === 0) {
              setTimeout(endGame, 500);
            }
            return { ...p, health: newHealth };
          });
          break;
        case 'strong':
          pointsGained = 50;
          coinsGained = 10;
          newCombo = prev.combo + 2;
          break;
      }

      const multiplier = Math.max(1, Math.floor(newCombo / 5));
      const finalScore = prev.score + (pointsGained * multiplier);

      console.log(`Points gained: ${pointsGained}, Multiplier: ${multiplier}, Final score: ${finalScore}`);

      return {
        ...prev,
        score: Math.max(0, finalScore),
        coins: prev.coins + coinsGained,
        combo: newCombo,
        level: Math.floor(finalScore / 500) + 1,
        speed: 1 + (Math.floor(finalScore / 500) * 0.2)
      };
    });

    // Remove collected item after animation
    setTimeout(() => {
      setCollectibles(prev => prev.filter(item => item.id !== id));
    }, 1000);

  }, [collectibles, endGame]);

  return {
    gameState,
    player,
    gameData,
    collectibles,
    highScore,
    selectedSprite,
    isNewHighScore: gameData.score > highScore && gameData.score > 0,
    actions: {
      moveLeft,
      moveRight,
      jump,
      startGame,
      pauseGame,
      resumeGame,
      endGame,
      returnToMenu,
      generateCollectible,
      collectItem,
      setCollectibles,
      selectSprite
    }
  };
};

export default useGameState;
