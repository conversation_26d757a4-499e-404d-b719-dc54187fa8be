
import React from 'react';
import PlayerSprites from './PlayerSprites';

interface PlayerProps {
  position: { x: number; y: number };
  isRunning: boolean;
  direction: 'left' | 'center' | 'right';
  powerUp?: string | null;
  selectedSprite: number;
}

const Player = ({ position, isRunning, direction, powerUp, selectedSprite }: PlayerProps) => {
  return (
    <PlayerSprites
      selectedSprite={selectedSprite}
      position={position}
      isRunning={isRunning}
      direction={direction}
      powerUp={powerUp}
    />
  );
};

export default Player;
