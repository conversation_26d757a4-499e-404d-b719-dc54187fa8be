
import React from 'react';
import { motion } from 'framer-motion';

interface PlayerSpritesProps {
  selectedSprite: number;
  position: { x: number; y: number };
  isRunning: boolean;
  direction: 'left' | 'center' | 'right';
  powerUp?: string | null;
}

const PlayerSprites = ({ selectedSprite, position, isRunning, direction, powerUp }: PlayerSpritesProps) => {
  const sprites = [
    { id: 0, src: '/lovable-uploads/player_sprites/sprite_1.png', name: 'Player 1', unlocked: true },
    { id: 1, src: '/lovable-uploads/player_sprites/sprite_2.png', name: 'Player 2', unlocked: true },
    { id: 2, src: '/lovable-uploads/player_sprites/sprite_3.png', name: 'Player 3', unlocked: true },
    { id: 3, src: '/lovable-uploads/player_sprites/sprite_4.png', name: 'Player 4', unlocked: true },
    { id: 4, src: '/lovable-uploads/player_sprites/sprite_5.png', name: 'Player 5', unlocked: false },
    { id: 5, src: '/lovable-uploads/player_sprites/sprite_6.png', name: 'Player 6', unlocked: false },
    { id: 6, src: '/lovable-uploads/player_sprites/sprite_7.png', name: 'Player 7', unlocked: false },
    { id: 7, src: '/lovable-uploads/player_sprites/sprite_8.png', name: 'Player 8', unlocked: false },
    { id: 8, src: '/lovable-uploads/player_sprites/sprite_9.png', name: 'Player 9', unlocked: false },
    { id: 9, src: '/lovable-uploads/player_sprites/sprite_10.png', name: 'Player 10', unlocked: false },
    { id: 10, src: '/lovable-uploads/player_sprites/sprite_11.png', name: 'Player 11', unlocked: false },
    { id: 11, src: '/lovable-uploads/player_sprites/sprite_12.png', name: 'Player 12', unlocked: false }
  ];

  const currentSprite = sprites[selectedSprite] || sprites[0];

  const getPlayerDirection = () => {
    switch (direction) {
      case 'left': return -20;
      case 'right': return 20;
      default: return 0;
    }
  };

  const getRunningAnimation = () => {
    if (!isRunning) return {};
    
    return {
      y: [0, -8, 0, -5, 0, -3, 0, -2, 0],
      transition: {
        duration: 0.8,
        repeat: Infinity,
        ease: "easeInOut"
      }
    };
  };

  return (
    <motion.div
      className="absolute z-30"
      style={{
        left: `${position.x}%`,
        bottom: `${position.y}%`,
        transform: 'translate(-50%, 0)'
      }}
      animate={{
        rotateY: getPlayerDirection(),
        ...getRunningAnimation(),
      }}
      transition={{ 
        rotateY: { duration: 0.25, ease: "easeOut" },
        x: { duration: 0.25, ease: "easeOut" }
      }}
    >
      {/* Enhanced Player Shadow with Motion */}
      <motion.div 
        className="absolute top-20 left-1/2 transform -translate-x-1/2 bg-black opacity-50 rounded-full blur-md"
        style={{ 
          width: powerUp ? '32px' : '28px',
          height: powerUp ? '14px' : '12px',
        }}
        animate={isRunning ? {
          scaleX: [1, 1.2, 1, 0.8, 1],
          opacity: [0.5, 0.3, 0.5, 0.4, 0.5]
        } : {}}
        transition={{ duration: 0.8, repeat: Infinity }}
      />
      
      {/* Power-up Aura Effect */}
      {powerUp && (
        <motion.div
          className="absolute -inset-8 rounded-full"
          animate={{
            scale: [1, 1.4, 1],
            opacity: [0.7, 1, 0.7],
            rotate: [0, 180, 360]
          }}
          transition={{
            duration: 1.2,
            repeat: Infinity,
            ease: "easeInOut"
          }}
          style={{
            background: 'radial-gradient(circle, rgba(255,215,0,0.9) 0%, rgba(255,215,0,0.5) 30%, rgba(255,165,0,0.3) 60%, transparent 100%)',
            filter: 'blur(4px)'
          }}
        />
      )}
      
      {/* Speed Boost Fire Trail */}
      {powerUp === 'fire' && isRunning && (
        <div className="absolute -left-20 top-10 w-full h-full pointer-events-none">
          {[...Array(10)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute w-8 h-8 rounded-full"
              style={{
                background: i % 2 === 0 
                  ? 'radial-gradient(circle, #FF6B35 0%, #F7931E 50%, rgba(255,107,53,0.5) 80%, transparent 100%)'
                  : 'radial-gradient(circle, #F7931E 0%, #FF6B35 50%, rgba(247,147,30,0.5) 80%, transparent 100%)',
                left: -i * 14,
                top: i * 3 + Math.sin(i * 0.6) * 5
              }}
              animate={{
                scale: [1, 1.8, 0.6, 0],
                opacity: [1, 0.8, 0.5, 0],
                rotate: [0, 180, 360]
              }}
              transition={{
                duration: 0.7,
                repeat: Infinity,
                delay: i * 0.07,
                ease: "easeOut"
              }}
            />
          ))}
        </div>
      )}
      
      {/* Premium Cartoon Football Player Sprite */}
      <motion.div
        className="relative w-28 h-32 mx-auto"
        style={{
          filter: powerUp 
            ? 'drop-shadow(0 0 35px #FFD700) drop-shadow(10px 10px 20px rgba(0,0,0,0.5))'
            : 'drop-shadow(10px 10px 20px rgba(0,0,0,0.5))',
          transform: powerUp ? 'scale(1.25)' : 'scale(1.1)',
          transition: 'all 0.4s ease'
        }}
      >
        {/* Main Player Sprite */}
        <motion.img
          src={currentSprite.src}
          alt={currentSprite.name}
          className="w-full h-full object-contain"
          animate={isRunning ? {
            rotateZ: [0, 3, 0, -3, 0],
            scaleX: [1, 1.02, 1, 0.98, 1]
          } : {}}
          transition={{ duration: 0.8, repeat: Infinity }}
          style={{
            imageRendering: 'pixelated',
            filter: 'contrast(1.1) saturate(1.2)'
          }}
        />

        {/* Celebration Animation for Collecting Items */}
        {powerUp && (
          <motion.div
            className="absolute -top-16 left-1/2 transform -translate-x-1/2 text-3xl"
            animate={{
              y: [0, -12, 0],
              rotate: [0, 15, -15, 0],
              scale: [1, 1.3, 1]
            }}
            transition={{
              duration: 0.7,
              repeat: Infinity
            }}
          >
            💪
          </motion.div>
        )}
      </motion.div>

      {/* Enhanced Speed Lines with Motion */}
      {isRunning && (
        <motion.div
          className="absolute -left-20 top-12 space-y-4"
          initial={{ opacity: 0, x: 40 }}
          animate={{ 
            opacity: [0, 1, 0], 
            x: [-50, -100]
          }}
          transition={{ 
            duration: 0.6, 
            repeat: Infinity,
            ease: "easeOut"
          }}
        >
          {[...Array(10)].map((_, i) => (
            <motion.div 
              key={i} 
              className="h-1.5 bg-white opacity-90 rounded-full shadow-lg"
              style={{ 
                width: `${25 - i * 2.5}px`,
                marginLeft: `${i * 5}px`,
                background: `linear-gradient(90deg, rgba(255,255,255,0.9) 0%, rgba(255,255,255,0.5) 50%, transparent 100%)`
              }}
              animate={{
                scaleX: [1, 1.3, 0.7],
                opacity: [0.9, 0.5, 0.9]
              }}
              transition={{
                duration: 0.4,
                repeat: Infinity,
                delay: i * 0.06
              }}
            />
          ))}
        </motion.div>
      )}
    </motion.div>
  );
};

export default PlayerSprites;
