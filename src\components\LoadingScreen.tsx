
import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { preloadGameComponents } from '../utils/assetPreloader';
import { preloadAllGameComponents } from '../utils/componentPreloader';

interface LoadingScreenProps {
  onLoadingComplete: () => void;
}

const LoadingScreen = ({ onLoadingComplete }: LoadingScreenProps) => {
  const [progress, setProgress] = useState(0);
  const [loadingStage, setLoadingStage] = useState('Initializing...');
  const [isComplete, setIsComplete] = useState(false);
  const [assetsReady, setAssetsReady] = useState(false);

  const loadingStages = [
    'Initializing ScrollFit Engine...',
    'Loading Game Components...',
    'Preparing Player Assets...',
    'Loading Game Field & UI...',
    'Initializing Touch Controls...',
    'Caching Game Data...',
    'Optimizing Performance...',
    'Ready to Play!'
  ];

  // Preload all game components and assets for offline play
  useEffect(() => {
    const loadGameComponents = async () => {
      try {
        console.log('🎮 Starting comprehensive offline game loading...');

        // Run both asset and component preloading in parallel
        const [assetsLoaded, componentsLoaded] = await Promise.all([
          preloadGameComponents(),
          preloadAllGameComponents()
        ]);

        console.log('✅ All game assets and components loaded successfully');
        console.log('📊 Assets loaded:', assetsLoaded !== false);
        console.log('📊 Components loaded:', componentsLoaded);

        setAssetsReady(true);
      } catch (error) {
        console.warn('⚠️ Some game components failed to load, continuing anyway:', error);
        setAssetsReady(true);
      }
    };

    loadGameComponents();
  }, []);

  useEffect(() => {
    if (!assetsReady) return;

    const loadGame = async () => {
      // Ensure exactly 5 seconds total display time for offline game
      const totalDisplayTime = 5000; // Exactly 5 seconds
      const stageTime = totalDisplayTime / loadingStages.length;

      console.log(`Loading screen will display for ${totalDisplayTime}ms with ${loadingStages.length} stages`);

      for (let i = 0; i < loadingStages.length; i++) {
        setLoadingStage(loadingStages[i]);

        // Distribute the time evenly across all stages
        const currentStageTime = i === loadingStages.length - 1 ? stageTime * 0.7 : stageTime;

        console.log(`Stage ${i + 1}: ${loadingStages[i]} - ${currentStageTime}ms`);

        await new Promise(resolve => setTimeout(resolve, currentStageTime));

        setProgress((i + 1) / loadingStages.length * 100);
      }

      setIsComplete(true);
      console.log('🎯 Loading animation complete, transitioning to game...');
      console.log('🚀 Game is ready for smooth offline play!');

      // Brief pause to show completion before transitioning
      setTimeout(() => {
        // Trigger garbage collection if available (for memory optimization)
        if (window.gc) {
          window.gc();
        }
        onLoadingComplete();
      }, 300);
    };

    loadGame();
  }, [onLoadingComplete, assetsReady]);

  if (!assetsReady) {
    return (
      <div className="loading-screen-container fixed inset-0 z-50 bg-black flex items-center justify-center">
        <motion.div
          animate={{ opacity: [0.5, 1, 0.5] }}
          transition={{ duration: 2, repeat: Infinity }}
          className="text-white text-lg font-medium"
        >
          Loading Assets...
        </motion.div>
      </div>
    );
  }

  return (
    <div className="loading-screen-container fixed inset-0 z-50 overflow-hidden">
      {/* Full-Screen Background Image with perfect coverage */}
      <motion.div
        className="absolute inset-0 w-full h-full"
        style={{
          backgroundImage: 'url(/scrollfit.png)',
          backgroundSize: 'cover',
          backgroundPosition: 'center center',
          backgroundRepeat: 'no-repeat',
          backgroundAttachment: 'fixed',
          /* Ensure no gaps on any device */
          width: '100vw',
          height: '100vh',
          minHeight: '100vh',
          /* Support for dynamic viewport on mobile */
          minHeight: '100dvh'
        }}
        animate={{
          scale: [1, 1.02, 1],
          filter: [
            'brightness(1) contrast(1)',
            'brightness(1.05) contrast(1.1)',
            'brightness(1) contrast(1)'
          ]
        }}
        transition={{
          duration: 4,
          repeat: Infinity,
          ease: "easeInOut"
        }}
      >
        {/* Subtle overlay for better text readability */}
        <div
          className="absolute inset-0"
          style={{
            background: 'linear-gradient(to bottom, rgba(0,0,0,0.1) 0%, rgba(0,0,0,0.4) 100%)',
            backdropFilter: 'blur(1px)'
          }}
        />

        {/* Animated particles for enhanced visual appeal */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          {[...Array(15)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute w-1 h-1 sm:w-2 sm:h-2 bg-white/20 rounded-full"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
              }}
              animate={{
                opacity: [0.2, 0.8, 0.2],
                scale: [1, 1.5, 1],
                y: [0, -20, 0]
              }}
              transition={{
                duration: 3 + Math.random() * 2,
                repeat: Infinity,
                delay: Math.random() * 2,
                ease: "easeInOut"
              }}
            />
          ))}
        </div>
      </motion.div>

      {/* Loading Animation Container - Positioned at bottom */}
      <div className="absolute inset-x-0 bottom-0 flex flex-col items-center justify-end pb-8 sm:pb-12 md:pb-16 lg:pb-20">
        {/* Loading Progress Section */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, ease: "easeOut" }}
          className="w-full max-w-xs sm:max-w-sm md:max-w-md lg:max-w-lg mx-auto px-6 sm:px-8"
        >
          {/* Progress Bar */}
          <div
            className="w-full h-2 sm:h-3 md:h-4 rounded-full mb-3 sm:mb-4 md:mb-6 overflow-hidden border border-white/30 shadow-lg"
            style={{
              background: 'rgba(0, 0, 0, 0.4)',
              backdropFilter: 'blur(20px)',
              boxShadow: '0 4px 20px rgba(0, 0, 0, 0.3)'
            }}
          >
            <motion.div
              className="h-full rounded-full relative overflow-hidden"
              style={{
                background: 'linear-gradient(90deg, #39FF14 0%, #00FF88 50%, #39FF14 100%)',
                width: `${progress}%`,
                boxShadow: '0 0 20px rgba(57, 255, 20, 0.6)'
              }}
              initial={{ width: 0 }}
              animate={{ width: `${progress}%` }}
              transition={{ duration: 0.3, ease: "easeOut" }}
            >
              {/* Enhanced shimmer effect */}
              <motion.div
                className="absolute inset-0 opacity-70"
                animate={{
                  background: [
                    'linear-gradient(90deg, transparent 0%, rgba(255,255,255,0.6) 50%, transparent 100%)',
                    'linear-gradient(90deg, transparent 0%, rgba(255,255,255,0.6) 50%, transparent 100%)'
                  ],
                  x: ['-100%', '200%']
                }}
                transition={{ duration: 1.5, repeat: Infinity, ease: "linear" }}
              />

              {/* Pulsing glow effect */}
              <motion.div
                className="absolute inset-0 rounded-full"
                animate={{
                  boxShadow: [
                    '0 0 10px rgba(57, 255, 20, 0.4)',
                    '0 0 25px rgba(57, 255, 20, 0.8)',
                    '0 0 10px rgba(57, 255, 20, 0.4)'
                  ]
                }}
                transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
              />
            </motion.div>
          </div>

          {/* Loading Text and Percentage */}
          <div className="text-center">
            {/* Loading Stage Text */}
            <motion.div
              key={loadingStage}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              className="text-white font-bold text-sm sm:text-base md:text-lg lg:text-xl mb-2 sm:mb-3 px-2"
              style={{
                textShadow: '2px 2px 8px rgba(0, 0, 0, 0.8)',
                backdropFilter: 'blur(10px)'
              }}
            >
              {loadingStage}
            </motion.div>

            {/* Progress Percentage */}
            <motion.div
              className="text-[#39FF14] font-black text-3xl sm:text-4xl md:text-5xl lg:text-6xl"
              animate={{
                scale: [1, 1.05, 1],
                textShadow: [
                  '0 0 20px rgba(57, 255, 20, 0.8)',
                  '0 0 30px rgba(57, 255, 20, 1)',
                  '0 0 20px rgba(57, 255, 20, 0.8)'
                ]
              }}
              transition={{ duration: 0.6, repeat: Infinity }}
              style={{
                textShadow: '0 0 20px rgba(57, 255, 20, 0.8), 2px 2px 8px rgba(0, 0, 0, 0.8)'
              }}
            >
              {Math.round(progress)}%
            </motion.div>
          </div>
        </motion.div>
      </div>

      {/* Completion Animation */}
      <AnimatePresence>
        {isComplete && (
          <motion.div
            initial={{ scale: 0, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 1.1, opacity: 0 }}
            className="absolute inset-0 flex items-center justify-center"
            style={{
              background: 'rgba(57, 255, 20, 0.15)',
              backdropFilter: 'blur(15px)'
            }}
          >
            <motion.div
              animate={{
                scale: [1, 1.2, 1],
                rotate: [0, 360],
              }}
              transition={{ duration: 0.8, ease: "easeInOut" }}
              className="text-6xl sm:text-7xl md:text-8xl lg:text-9xl"
              style={{
                filter: 'drop-shadow(0 0 20px rgba(57, 255, 20, 0.8))'
              }}
            >
              ✅
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default LoadingScreen;
