
import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { preloadGameComponents } from '../utils/assetPreloader';
import { preloadAllGameComponents } from '../utils/componentPreloader';

interface LoadingScreenProps {
  onLoadingComplete: () => void;
}

const LoadingScreen = ({ onLoadingComplete }: LoadingScreenProps) => {
  const [progress, setProgress] = useState(0);
  const [loadingStage, setLoadingStage] = useState('Initializing...');
  const [isComplete, setIsComplete] = useState(false);
  const [assetsReady, setAssetsReady] = useState(false);

  const loadingStages = [
    'Initializing ScrollFit Engine...',
    'Loading Game Components...',
    'Preparing Player Assets...',
    'Loading Game Field & UI...',
    'Initializing Touch Controls...',
    'Caching Game Data...',
    'Optimizing Performance...',
    'Ready to Play!'
  ];

  // Preload all game components and assets for offline play
  useEffect(() => {
    const loadGameComponents = async () => {
      try {
        console.log('🎮 Starting comprehensive offline game loading...');

        // Run both asset and component preloading in parallel
        const [assetsLoaded, componentsLoaded] = await Promise.all([
          preloadGameComponents(),
          preloadAllGameComponents()
        ]);

        console.log('✅ All game assets and components loaded successfully');
        console.log('📊 Assets loaded:', assetsLoaded !== false);
        console.log('📊 Components loaded:', componentsLoaded);

        setAssetsReady(true);
      } catch (error) {
        console.warn('⚠️ Some game components failed to load, continuing anyway:', error);
        setAssetsReady(true);
      }
    };

    loadGameComponents();
  }, []);

  useEffect(() => {
    if (!assetsReady) return;

    const loadGame = async () => {
      // Ensure exactly 5 seconds total display time for offline game
      const totalDisplayTime = 5000; // Exactly 5 seconds
      const stageTime = totalDisplayTime / loadingStages.length;

      console.log(`Loading screen will display for ${totalDisplayTime}ms with ${loadingStages.length} stages`);

      for (let i = 0; i < loadingStages.length; i++) {
        setLoadingStage(loadingStages[i]);

        // Distribute the time evenly across all stages
        const currentStageTime = i === loadingStages.length - 1 ? stageTime * 0.7 : stageTime;

        console.log(`Stage ${i + 1}: ${loadingStages[i]} - ${currentStageTime}ms`);

        await new Promise(resolve => setTimeout(resolve, currentStageTime));

        setProgress((i + 1) / loadingStages.length * 100);
      }

      setIsComplete(true);
      console.log('🎯 Loading animation complete, transitioning to game...');
      console.log('🚀 Game is ready for smooth offline play!');

      // Brief pause to show completion before transitioning
      setTimeout(() => {
        // Trigger garbage collection if available (for memory optimization)
        if (window.gc) {
          window.gc();
        }
        onLoadingComplete();
      }, 300);
    };

    loadGame();
  }, [onLoadingComplete, assetsReady]);

  if (!assetsReady) {
    return (
      <div className="loading-screen-container fixed inset-0 z-50 bg-black flex items-center justify-center">
        <motion.div
          animate={{ opacity: [0.5, 1, 0.5] }}
          transition={{ duration: 2, repeat: Infinity }}
          className="text-white text-lg font-medium"
        >
          Loading Assets...
        </motion.div>
      </div>
    );
  }

  return (
    <div className="loading-screen-container fixed inset-0 z-50 overflow-hidden">
      {/* Full-Screen Background Image with perfect coverage */}
      <motion.div
        className="absolute inset-0 w-full h-full"
        style={{
          backgroundImage: 'url(/scrollfit.png)',
          backgroundSize: 'cover',
          backgroundPosition: 'center center',
          backgroundRepeat: 'no-repeat',
          backgroundAttachment: 'fixed',
          /* Ensure no gaps on any device */
          width: '100vw',
          height: '100vh',
          minHeight: '100vh',
          /* Support for dynamic viewport on mobile */
          minHeight: '100dvh'
        }}
        animate={{
          scale: [1, 1.02, 1],
          filter: [
            'brightness(1) contrast(1)',
            'brightness(1.05) contrast(1.1)',
            'brightness(1) contrast(1)'
          ]
        }}
        transition={{
          duration: 4,
          repeat: Infinity,
          ease: "easeInOut"
        }}
      >
        {/* Subtle overlay for better text readability - NO BLUR */}
        <div
          className="absolute inset-0"
          style={{
            background: 'linear-gradient(to bottom, rgba(0,0,0,0.1) 0%, rgba(0,0,0,0.4) 100%)'
          }}
        />

        {/* Enhanced Animated particles for cooler visual appeal */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          {/* Main floating particles */}
          {[...Array(20)].map((_, i) => (
            <motion.div
              key={`particle-${i}`}
              className="absolute rounded-full"
              style={{
                width: `${2 + Math.random() * 4}px`,
                height: `${2 + Math.random() * 4}px`,
                background: `rgba(${57 + Math.random() * 50}, ${255 - Math.random() * 50}, ${20 + Math.random() * 50}, ${0.3 + Math.random() * 0.4})`,
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
                boxShadow: `0 0 ${10 + Math.random() * 10}px rgba(57, 255, 20, 0.6)`
              }}
              animate={{
                opacity: [0.2, 0.9, 0.2],
                scale: [1, 1.8, 1],
                y: [0, -30 - Math.random() * 20, 0],
                x: [0, (Math.random() - 0.5) * 40, 0]
              }}
              transition={{
                duration: 4 + Math.random() * 3,
                repeat: Infinity,
                delay: Math.random() * 3,
                ease: "easeInOut"
              }}
            />
          ))}

          {/* Energy streaks */}
          {[...Array(8)].map((_, i) => (
            <motion.div
              key={`streak-${i}`}
              className="absolute"
              style={{
                width: '2px',
                height: `${20 + Math.random() * 30}px`,
                background: 'linear-gradient(to bottom, transparent, #39FF14, transparent)',
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
                borderRadius: '1px'
              }}
              animate={{
                opacity: [0, 1, 0],
                scaleY: [0.5, 1.5, 0.5],
                rotate: [0, 180, 360]
              }}
              transition={{
                duration: 2 + Math.random() * 2,
                repeat: Infinity,
                delay: Math.random() * 4,
                ease: "linear"
              }}
            />
          ))}
        </div>
      </motion.div>

      {/* Loading Animation Container - Positioned at bottom */}
      <div className="absolute inset-x-0 bottom-0 flex flex-col items-center justify-end pb-8 sm:pb-12 md:pb-16 lg:pb-20">
        {/* Loading Progress Section */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, ease: "easeOut" }}
          className="w-full max-w-xs sm:max-w-sm md:max-w-md lg:max-w-lg mx-auto px-6 sm:px-8"
        >
          {/* Enhanced Progress Bar - NO BLUR */}
          <div
            className="w-full h-3 sm:h-4 md:h-5 rounded-full mb-3 sm:mb-4 md:mb-6 overflow-hidden border-2 border-white/40 shadow-lg relative"
            style={{
              background: 'rgba(0, 0, 0, 0.6)',
              boxShadow: '0 4px 20px rgba(0, 0, 0, 0.4), inset 0 2px 4px rgba(255, 255, 255, 0.1)'
            }}
          >
            {/* Background glow effect */}
            <motion.div
              className="absolute inset-0 rounded-full"
              animate={{
                boxShadow: [
                  'inset 0 0 20px rgba(57, 255, 20, 0.1)',
                  'inset 0 0 30px rgba(57, 255, 20, 0.3)',
                  'inset 0 0 20px rgba(57, 255, 20, 0.1)'
                ]
              }}
              transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
            />

            <motion.div
              className="h-full rounded-full relative overflow-hidden"
              style={{
                background: 'linear-gradient(90deg, #39FF14 0%, #00FF88 30%, #39FF14 60%, #00FF88 100%)',
                width: `${progress}%`,
                boxShadow: '0 0 25px rgba(57, 255, 20, 0.8), 0 0 50px rgba(57, 255, 20, 0.4)'
              }}
              initial={{ width: 0 }}
              animate={{
                width: `${progress}%`,
                background: [
                  'linear-gradient(90deg, #39FF14 0%, #00FF88 30%, #39FF14 60%, #00FF88 100%)',
                  'linear-gradient(90deg, #00FF88 0%, #39FF14 30%, #00FF88 60%, #39FF14 100%)',
                  'linear-gradient(90deg, #39FF14 0%, #00FF88 30%, #39FF14 60%, #00FF88 100%)'
                ]
              }}
              transition={{
                width: { duration: 0.3, ease: "easeOut" },
                background: { duration: 3, repeat: Infinity, ease: "easeInOut" }
              }}
            >
              {/* Enhanced multi-layer shimmer effect */}
              <motion.div
                className="absolute inset-0 opacity-80"
                animate={{
                  background: [
                    'linear-gradient(90deg, transparent 0%, rgba(255,255,255,0.8) 50%, transparent 100%)',
                    'linear-gradient(90deg, transparent 0%, rgba(255,255,255,0.8) 50%, transparent 100%)'
                  ],
                  x: ['-100%', '200%']
                }}
                transition={{ duration: 1.2, repeat: Infinity, ease: "linear" }}
              />

              {/* Secondary shimmer wave */}
              <motion.div
                className="absolute inset-0 opacity-50"
                animate={{
                  background: [
                    'linear-gradient(90deg, transparent 0%, rgba(57,255,20,0.9) 50%, transparent 100%)',
                    'linear-gradient(90deg, transparent 0%, rgba(57,255,20,0.9) 50%, transparent 100%)'
                  ],
                  x: ['-150%', '250%']
                }}
                transition={{ duration: 2, repeat: Infinity, ease: "linear", delay: 0.5 }}
              />

              {/* Enhanced pulsing glow effect */}
              <motion.div
                className="absolute inset-0 rounded-full"
                animate={{
                  boxShadow: [
                    '0 0 15px rgba(57, 255, 20, 0.6), 0 0 30px rgba(57, 255, 20, 0.3)',
                    '0 0 30px rgba(57, 255, 20, 1), 0 0 60px rgba(57, 255, 20, 0.6)',
                    '0 0 15px rgba(57, 255, 20, 0.6), 0 0 30px rgba(57, 255, 20, 0.3)'
                  ]
                }}
                transition={{ duration: 1.5, repeat: Infinity, ease: "easeInOut" }}
              />
            </motion.div>
          </div>

          {/* Loading Text and Percentage */}
          <div className="text-center">
            {/* Enhanced Loading Stage Text - NO BLUR */}
            <motion.div
              key={loadingStage}
              initial={{ opacity: 0, y: 10 }}
              animate={{
                opacity: 1,
                y: 0,
                textShadow: [
                  '2px 2px 8px rgba(0, 0, 0, 0.8), 0 0 10px rgba(57, 255, 20, 0.3)',
                  '2px 2px 8px rgba(0, 0, 0, 0.8), 0 0 20px rgba(57, 255, 20, 0.6)',
                  '2px 2px 8px rgba(0, 0, 0, 0.8), 0 0 10px rgba(57, 255, 20, 0.3)'
                ]
              }}
              exit={{ opacity: 0, y: -10 }}
              transition={{
                opacity: { duration: 0.3 },
                y: { duration: 0.3 },
                textShadow: { duration: 2, repeat: Infinity, ease: "easeInOut" }
              }}
              className="text-white font-bold text-sm sm:text-base md:text-lg lg:text-xl mb-2 sm:mb-3 px-2"
              style={{
                textShadow: '2px 2px 8px rgba(0, 0, 0, 0.8), 0 0 15px rgba(57, 255, 20, 0.4)'
              }}
            >
              {loadingStage}
            </motion.div>

            {/* Enhanced Progress Percentage */}
            <motion.div
              className="text-[#39FF14] font-black text-3xl sm:text-4xl md:text-5xl lg:text-6xl relative"
              animate={{
                scale: [1, 1.08, 1],
                textShadow: [
                  '0 0 20px rgba(57, 255, 20, 0.8), 0 0 40px rgba(57, 255, 20, 0.4)',
                  '0 0 35px rgba(57, 255, 20, 1), 0 0 70px rgba(57, 255, 20, 0.7)',
                  '0 0 20px rgba(57, 255, 20, 0.8), 0 0 40px rgba(57, 255, 20, 0.4)'
                ]
              }}
              transition={{ duration: 0.8, repeat: Infinity, ease: "easeInOut" }}
              style={{
                textShadow: '0 0 25px rgba(57, 255, 20, 0.9), 2px 2px 8px rgba(0, 0, 0, 0.8), 0 0 50px rgba(57, 255, 20, 0.5)'
              }}
            >
              {Math.round(progress)}%

              {/* Percentage glow overlay */}
              <motion.div
                className="absolute inset-0 text-[#39FF14] font-black text-3xl sm:text-4xl md:text-5xl lg:text-6xl opacity-50"
                animate={{
                  scale: [1.02, 1.1, 1.02],
                  opacity: [0.3, 0.7, 0.3]
                }}
                transition={{ duration: 1.2, repeat: Infinity, ease: "easeInOut", delay: 0.3 }}
                style={{
                  filter: 'blur(2px)',
                  textShadow: '0 0 30px rgba(57, 255, 20, 1)'
                }}
              >
                {Math.round(progress)}%
              </motion.div>
            </motion.div>
          </div>
        </motion.div>
      </div>

      {/* Enhanced Completion Animation - NO BLUR */}
      <AnimatePresence>
        {isComplete && (
          <motion.div
            initial={{ scale: 0, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 1.1, opacity: 0 }}
            className="absolute inset-0 flex items-center justify-center"
            style={{
              background: 'rgba(57, 255, 20, 0.2)'
            }}
          >
            {/* Completion burst effect */}
            <div className="absolute inset-0 overflow-hidden pointer-events-none">
              {[...Array(12)].map((_, i) => (
                <motion.div
                  key={`burst-${i}`}
                  className="absolute w-2 h-2 bg-[#39FF14] rounded-full"
                  style={{
                    left: '50%',
                    top: '50%',
                    boxShadow: '0 0 10px rgba(57, 255, 20, 0.8)'
                  }}
                  initial={{ scale: 0, x: 0, y: 0 }}
                  animate={{
                    scale: [0, 1, 0],
                    x: [0, Math.cos(i * 30 * Math.PI / 180) * 200],
                    y: [0, Math.sin(i * 30 * Math.PI / 180) * 200],
                    opacity: [1, 0.8, 0]
                  }}
                  transition={{
                    duration: 1.5,
                    ease: "easeOut",
                    delay: i * 0.05
                  }}
                />
              ))}
            </div>

            <motion.div
              animate={{
                scale: [1, 1.3, 1.1],
                rotate: [0, 360, 720],
              }}
              transition={{ duration: 1.2, ease: "easeInOut" }}
              className="text-6xl sm:text-7xl md:text-8xl lg:text-9xl relative z-10"
              style={{
                filter: 'drop-shadow(0 0 30px rgba(57, 255, 20, 1)) drop-shadow(0 0 60px rgba(57, 255, 20, 0.6))'
              }}
            >
              ✅

              {/* Completion glow rings */}
              <motion.div
                className="absolute inset-0 flex items-center justify-center"
                animate={{
                  scale: [1, 2, 3],
                  opacity: [0.8, 0.4, 0]
                }}
                transition={{ duration: 1.5, ease: "easeOut" }}
              >
                <div
                  className="w-20 h-20 border-4 border-[#39FF14] rounded-full"
                  style={{
                    boxShadow: '0 0 20px rgba(57, 255, 20, 0.8)'
                  }}
                />
              </motion.div>

              <motion.div
                className="absolute inset-0 flex items-center justify-center"
                animate={{
                  scale: [1, 1.5, 2.5],
                  opacity: [0.6, 0.3, 0]
                }}
                transition={{ duration: 1.8, ease: "easeOut", delay: 0.2 }}
              >
                <div
                  className="w-32 h-32 border-2 border-[#00FF88] rounded-full"
                  style={{
                    boxShadow: '0 0 15px rgba(0, 255, 136, 0.6)'
                  }}
                />
              </motion.div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default LoadingScreen;
