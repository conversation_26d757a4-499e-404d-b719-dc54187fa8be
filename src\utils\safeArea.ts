/**
 * Safe Area Utilities for Mobile Devices
 * Handles iOS notch, Android navigation bars, and other device-specific UI elements
 */

export interface SafeAreaInsets {
  top: number;
  bottom: number;
  left: number;
  right: number;
}

/**
 * Get safe area insets from CSS environment variables
 */
export const getSafeAreaInsets = (): SafeAreaInsets => {
  // Check if CSS environment variables are supported
  if (typeof window === 'undefined' || !window.CSS?.supports?.('padding: env(safe-area-inset-top)')) {
    return { top: 0, bottom: 0, left: 0, right: 0 };
  }

  // Create a temporary element to measure safe area insets
  const testElement = document.createElement('div');
  testElement.style.position = 'fixed';
  testElement.style.top = '0';
  testElement.style.left = '0';
  testElement.style.width = '1px';
  testElement.style.height = '1px';
  testElement.style.visibility = 'hidden';
  testElement.style.paddingTop = 'env(safe-area-inset-top)';
  testElement.style.paddingBottom = 'env(safe-area-inset-bottom)';
  testElement.style.paddingLeft = 'env(safe-area-inset-left)';
  testElement.style.paddingRight = 'env(safe-area-inset-right)';

  document.body.appendChild(testElement);

  const computedStyle = window.getComputedStyle(testElement);
  const insets: SafeAreaInsets = {
    top: parseFloat(computedStyle.paddingTop) || 0,
    bottom: parseFloat(computedStyle.paddingBottom) || 0,
    left: parseFloat(computedStyle.paddingLeft) || 0,
    right: parseFloat(computedStyle.paddingRight) || 0,
  };

  document.body.removeChild(testElement);
  return insets;
};

/**
 * Check if device has safe area insets (like iPhone X+ with notch)
 */
export const hasSafeAreaInsets = (): boolean => {
  const insets = getSafeAreaInsets();
  return insets.top > 0 || insets.bottom > 0 || insets.left > 0 || insets.right > 0;
};

/**
 * Get viewport dimensions accounting for safe areas
 */
export const getViewportDimensions = () => {
  const insets = getSafeAreaInsets();
  
  return {
    width: window.innerWidth - insets.left - insets.right,
    height: window.innerHeight - insets.top - insets.bottom,
    fullWidth: window.innerWidth,
    fullHeight: window.innerHeight,
    insets
  };
};

/**
 * Generate CSS custom properties for safe area insets
 */
export const generateSafeAreaCSS = (): string => {
  const insets = getSafeAreaInsets();
  
  return `
    --safe-area-inset-top: ${insets.top}px;
    --safe-area-inset-bottom: ${insets.bottom}px;
    --safe-area-inset-left: ${insets.left}px;
    --safe-area-inset-right: ${insets.right}px;
    --safe-area-width: calc(100vw - ${insets.left}px - ${insets.right}px);
    --safe-area-height: calc(100vh - ${insets.top}px - ${insets.bottom}px);
  `;
};

/**
 * Apply safe area styles to an element
 */
export const applySafeAreaStyles = (element: HTMLElement, options: {
  respectTop?: boolean;
  respectBottom?: boolean;
  respectLeft?: boolean;
  respectRight?: boolean;
} = {}) => {
  const {
    respectTop = true,
    respectBottom = true,
    respectLeft = true,
    respectRight = true
  } = options;

  const insets = getSafeAreaInsets();

  if (respectTop && insets.top > 0) {
    element.style.paddingTop = `max(${element.style.paddingTop || '0px'}, ${insets.top}px)`;
  }
  
  if (respectBottom && insets.bottom > 0) {
    element.style.paddingBottom = `max(${element.style.paddingBottom || '0px'}, ${insets.bottom}px)`;
  }
  
  if (respectLeft && insets.left > 0) {
    element.style.paddingLeft = `max(${element.style.paddingLeft || '0px'}, ${insets.left}px)`;
  }
  
  if (respectRight && insets.right > 0) {
    element.style.paddingRight = `max(${element.style.paddingRight || '0px'}, ${insets.right}px)`;
  }
};

/**
 * React hook for safe area insets
 */
export const useSafeAreaInsets = () => {
  const [insets, setInsets] = React.useState<SafeAreaInsets>({ top: 0, bottom: 0, left: 0, right: 0 });

  React.useEffect(() => {
    const updateInsets = () => {
      setInsets(getSafeAreaInsets());
    };

    // Update on mount
    updateInsets();

    // Update on orientation change
    window.addEventListener('orientationchange', updateInsets);
    window.addEventListener('resize', updateInsets);

    return () => {
      window.removeEventListener('orientationchange', updateInsets);
      window.removeEventListener('resize', updateInsets);
    };
  }, []);

  return insets;
};

// Export React for the hook
import React from 'react';
