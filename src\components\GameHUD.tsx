
import React from 'react';
import { motion } from 'framer-motion';

interface GameHUDProps {
  score: number;
  health: number;
  coins: number;
  combo: number;
  level: number;
}

const GameHUD = ({ score, health, coins, combo, level }: GameHUDProps) => {
  return (
    <div className="absolute top-0 left-0 right-0 z-50 p-2 sm:p-4">
      {/* Top Row - Premium Glassmorphism HUD - Mobile Responsive */}
      <div className="flex justify-between items-start mb-2 sm:mb-4 gap-1 sm:gap-2">
        {/* Score Display with Glassmorphism */}
        <motion.div
          initial={{ scale: 0, x: -100 }}
          animate={{
            scale: combo > 1 ? [1, 1.15, 1] : 1,
            x: 0
          }}
          transition={{
            scale: { duration: 0.4, repeat: combo > 1 ? Infinity : 0 },
            x: { duration: 0.6, ease: "easeOut" }
          }}
          className="rounded-2xl sm:rounded-3xl px-2 sm:px-4 md:px-8 py-2 sm:py-4 shadow-2xl border border-white/30 flex-1 max-w-[120px] sm:max-w-none"
          style={{
            background: 'linear-gradient(135deg, rgba(139, 92, 246, 0.85) 0%, rgba(59, 130, 246, 0.85) 100%)',
            backdropFilter: 'blur(20px)',
            boxShadow: '0 12px 40px rgba(139, 92, 246, 0.4), inset 0 1px 0 rgba(255, 255, 255, 0.3)'
          }}
        >
          <div className="flex items-center space-x-1 sm:space-x-2 md:space-x-4">
            <motion.span
              className="text-lg sm:text-2xl md:text-4xl"
              animate={{
                rotate: score > 0 ? [0, 15, -15, 0] : 0,
                scale: [1, 1.1, 1]
              }}
              transition={{
                duration: 2,
                repeat: Infinity,
                ease: "easeInOut"
              }}
            >
              🏆
            </motion.span>
            <div className="text-white min-w-0">
              <div className="text-xs sm:text-sm font-bold opacity-90 tracking-wide">SCORE</div>
              <div className="font-black text-sm sm:text-lg md:text-2xl tracking-tight drop-shadow-lg truncate">
                {score.toLocaleString()}
              </div>
            </div>
          </div>
        </motion.div>

        {/* Level Display */}
        <motion.div
          initial={{ scale: 0, y: -50 }}
          animate={{ scale: 1, y: 0 }}
          className="rounded-2xl sm:rounded-3xl px-2 sm:px-4 md:px-6 py-2 sm:py-4 shadow-2xl border border-white/30"
          style={{
            background: 'linear-gradient(135deg, rgba(249, 115, 22, 0.9) 0%, rgba(239, 68, 68, 0.9) 100%)',
            backdropFilter: 'blur(20px)',
            boxShadow: '0 12px 40px rgba(249, 115, 22, 0.4), inset 0 1px 0 rgba(255, 255, 255, 0.3)'
          }}
        >
          <div className="text-center text-white">
            <div className="text-xs sm:text-sm font-bold opacity-90 tracking-wide">LEVEL</div>
            <motion.div
              className="font-black text-lg sm:text-2xl md:text-3xl drop-shadow-lg"
              animate={{ scale: level > 1 ? [1, 1.2, 1] : 1 }}
              transition={{ duration: 0.6 }}
            >
              {level}
            </motion.div>
          </div>
        </motion.div>

        {/* Coins Display */}
        <motion.div
          initial={{ scale: 0, x: 100 }}
          animate={{ scale: 1, x: 0 }}
          className="rounded-2xl sm:rounded-3xl px-2 sm:px-4 md:px-6 py-2 sm:py-4 shadow-2xl border border-white/30 flex-1 max-w-[100px] sm:max-w-none"
          style={{
            background: 'linear-gradient(135deg, rgba(251, 191, 36, 0.9) 0%, rgba(249, 115, 22, 0.9) 100%)',
            backdropFilter: 'blur(20px)',
            boxShadow: '0 12px 40px rgba(251, 191, 36, 0.4), inset 0 1px 0 rgba(255, 255, 255, 0.3)'
          }}
        >
          <div className="flex items-center space-x-1 sm:space-x-2 md:space-x-3">
            <motion.span
              className="text-lg sm:text-2xl md:text-3xl"
              animate={{
                rotate: [0, 360],
                scale: [1, 1.1, 1]
              }}
              transition={{
                rotate: { duration: 4, repeat: Infinity, ease: "linear" },
                scale: { duration: 2, repeat: Infinity }
              }}
            >
              🪙
            </motion.span>
            <div className="text-white min-w-0">
              <div className="text-xs sm:text-sm font-bold opacity-90 tracking-wide">COINS</div>
              <div className="font-black text-sm sm:text-lg md:text-xl drop-shadow-lg truncate">{coins}</div>
            </div>
          </div>
        </motion.div>
      </div>

      {/* Health Bar with Premium Glassmorphism - Mobile Responsive */}
      <div className="flex justify-center mb-3 sm:mb-6">
        <motion.div
          initial={{ scale: 0, y: -30 }}
          animate={{ scale: 1, y: 0 }}
          className="rounded-full p-2 sm:p-3 md:p-5 shadow-2xl border border-white/20"
          style={{
            background: 'rgba(0, 0, 0, 0.4)',
            backdropFilter: 'blur(25px)',
            boxShadow: '0 15px 50px rgba(0, 0, 0, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.2)'
          }}
        >
          <div className="flex space-x-2 sm:space-x-3 md:space-x-4">
            {[...Array(3)].map((_, i) => (
              <motion.div
                key={i}
                className={`w-8 h-8 sm:w-10 sm:h-10 md:w-14 md:h-14 rounded-full flex items-center justify-center text-lg sm:text-2xl md:text-3xl border-2 sm:border-3 ${
                  i < health
                    ? 'border-red-300 shadow-xl'
                    : 'border-gray-500 opacity-50'
                }`}
                style={{
                  background: i < health
                    ? 'linear-gradient(135deg, rgba(248, 113, 113, 0.9) 0%, rgba(220, 38, 38, 0.9) 100%)'
                    : 'rgba(107, 114, 128, 0.7)',
                  backdropFilter: 'blur(15px)',
                  boxShadow: i < health
                    ? '0 8px 30px rgba(220, 38, 38, 0.5), inset 0 2px 0 rgba(255, 255, 255, 0.3)'
                    : 'none'
                }}
                animate={i < health ? {
                  scale: [1, 1.15, 1],
                  boxShadow: [
                    '0 8px 30px rgba(220, 38, 38, 0.5)',
                    '0 12px 40px rgba(220, 38, 38, 0.7)',
                    '0 8px 30px rgba(220, 38, 38, 0.5)'
                  ]
                } : {}}
                transition={{
                  duration: 1.2,
                  repeat: i < health ? Infinity : 0,
                  delay: i * 0.2
                }}
              >
                <span className="drop-shadow-lg">❤️</span>
              </motion.div>
            ))}
          </div>
        </motion.div>
      </div>

      {/* Premium Combo Display - Mobile Responsive */}
      {combo > 1 && (
        <motion.div
          initial={{ scale: 0, y: -50 }}
          animate={{ scale: 1, y: 0 }}
          exit={{ scale: 0, y: -50 }}
          className="flex justify-center px-2"
        >
          <motion.div
            animate={{
              scale: [1, 1.3, 1],
              rotate: [0, 8, -8, 0]
            }}
            transition={{
              duration: 0.6,
              repeat: Infinity,
              ease: "easeInOut"
            }}
            className="rounded-full px-4 sm:px-6 md:px-10 py-2 sm:py-3 md:py-5 shadow-2xl border-2 sm:border-4 border-yellow-400"
            style={{
              background: 'linear-gradient(135deg, rgba(236, 72, 153, 0.95) 0%, rgba(239, 68, 68, 0.95) 100%)',
              backdropFilter: 'blur(25px)',
              boxShadow: '0 15px 50px rgba(236, 72, 153, 0.5), 0 0 0 3px #FBBF24, inset 0 2px 0 rgba(255, 255, 255, 0.3)'
            }}
          >
            <div className="flex items-center space-x-2 sm:space-x-3 md:space-x-4">
              <motion.span
                className="text-2xl sm:text-3xl md:text-4xl"
                animate={{
                  scale: [1, 1.4, 1],
                  rotate: [0, 360]
                }}
                transition={{
                  scale: { duration: 0.4, repeat: Infinity },
                  rotate: { duration: 1.2, repeat: Infinity }
                }}
              >
                🔥
              </motion.span>
              <div className="text-white text-center">
                <div className="font-black text-lg sm:text-2xl md:text-3xl tracking-wide drop-shadow-xl">
                  {combo}x COMBO!
                </div>
                <div className="text-xs sm:text-sm font-bold opacity-90 tracking-wider">
                  STREAK ACTIVE
                </div>
              </div>
            </div>
          </motion.div>
        </motion.div>
      )}

      {/* Progress Bar for Power-ups - Mobile Responsive */}
      <div className="absolute top-32 sm:top-36 md:top-40 left-2 right-2 sm:left-4 sm:right-4">
        <div
          className="h-2 sm:h-3 rounded-full overflow-hidden border border-white/30 sm:border-2 shadow-lg"
          style={{
            background: 'rgba(0, 0, 0, 0.3)',
            backdropFilter: 'blur(15px)'
          }}
        >
          <motion.div
            className="h-full rounded-full"
            initial={{ width: '0%' }}
            animate={{ width: '100%' }}
            transition={{ duration: 12, ease: "linear" }}
            style={{
              background: 'linear-gradient(90deg, #06D6A0 0%, #3B82F6 50%, #8B5CF6 100%)',
              boxShadow: '0 0 15px rgba(34, 211, 238, 0.6)'
            }}
          />
        </div>
        <div className="text-center mt-1 sm:mt-2">
          <span
            className="text-white text-xs sm:text-sm font-bold tracking-wide"
            style={{
              textShadow: '2px 2px 4px rgba(0,0,0,0.8)',
              backdropFilter: 'blur(10px)'
            }}
          >
            POWER-UP CHARGING
          </span>
        </div>
      </div>

      {/* Achievement Notification Area - Mobile Responsive */}
      <div className="absolute top-44 sm:top-48 md:top-52 left-1/2 transform -translate-x-1/2">
        {/* This area can be used for floating achievement notifications */}
      </div>
    </div>
  );
};

export default GameHUD;
