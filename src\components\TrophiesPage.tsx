
import React from 'react';
import { motion } from 'framer-motion';
import { ScrollArea } from '@/components/ui/scroll-area';
import { ArrowLeft, Trophy, Star, Zap, Crown, Target, Heart, Medal, Award } from 'lucide-react';

interface TrophiesPageProps {
  onBack: () => void;
}

const TrophiesPage = ({ onBack }: TrophiesPageProps) => {
  const trophies = [
    {
      id: 1,
      title: 'First Steps',
      description: 'Complete your first game',
      icon: Trophy,
      rarity: 'common',
      unlocked: true,
      unlockedDate: '2024-01-15',
      progress: 100,
      xp: 50
    },
    {
      id: 2,
      title: 'Speed Demon',
      description: 'Score 1000+ points in a single game',
      icon: Zap,
      rarity: 'rare',
      unlocked: true,
      unlockedDate: '2024-01-20',
      progress: 100,
      xp: 150
    },
    {
      id: 3,
      title: 'Wellness Warrior',
      description: 'Maintain 90% wellness for a week',
      icon: Heart,
      rarity: 'epic',
      unlocked: true,
      unlockedDate: '2024-02-01',
      progress: 100,
      xp: 300
    },
    {
      id: 4,
      title: 'Marathon Master',
      description: 'Play for 30 days straight',
      icon: Medal,
      rarity: 'legendary',
      unlocked: false,
      progress: 87,
      xp: 500
    },
    {
      id: 5,
      title: 'Perfect Score',
      description: 'Achieve a perfect wellness score of 100%',
      icon: Star,
      rarity: 'epic',
      unlocked: false,
      progress: 95,
      xp: 250
    },
    {
      id: 6,
      title: 'Collector Supreme',
      description: 'Collect 1000 healthy items',
      icon: Target,
      rarity: 'rare',
      unlocked: false,
      progress: 73,
      xp: 200
    },
    {
      id: 7,
      title: 'Champion',
      description: 'Reach the top of the leaderboard',
      icon: Crown,
      rarity: 'legendary',
      unlocked: false,
      progress: 12,
      xp: 1000
    },
    {
      id: 8,
      title: 'Mental Athlete',
      description: 'Complete 100 games',
      icon: Award,
      rarity: 'epic',
      unlocked: true,
      unlockedDate: '2024-02-15',
      progress: 100,
      xp: 400
    }
  ];

  const getRarityColor = (rarity: string) => {
    switch (rarity) {
      case 'common': return '#9CA3AF';
      case 'rare': return '#00D4FF';
      case 'epic': return '#8B5CF6';
      case 'legendary': return '#FFD700';
      default: return '#9CA3AF';
    }
  };

  const getRarityGlow = (rarity: string) => {
    switch (rarity) {
      case 'common': return 'rgba(156, 163, 175, 0.3)';
      case 'rare': return 'rgba(0, 212, 255, 0.3)';
      case 'epic': return 'rgba(139, 92, 246, 0.3)';
      case 'legendary': return 'rgba(255, 215, 0, 0.3)';
      default: return 'rgba(156, 163, 175, 0.3)';
    }
  };

  const unlockedTrophies = trophies.filter(t => t.unlocked).length;
  const totalXP = trophies.filter(t => t.unlocked).reduce((sum, t) => sum + t.xp, 0);

  return (
    <div className="min-h-screen w-full relative overflow-hidden" style={{
      background: 'linear-gradient(135deg, #0A0B1A 0%, #161B33 50%, #0D1117 100%)'
    }}>
      {/* Animated Background */}
      <div className="absolute inset-0 overflow-hidden">
        {[...Array(30)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-2 h-2 bg-white/10 rounded-full"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
            animate={{
              opacity: [0.1, 0.6, 0.1],
              scale: [1, 2, 1],
            }}
            transition={{
              duration: 3 + Math.random() * 2,
              repeat: Infinity,
              delay: Math.random() * 2,
            }}
          />
        ))}
      </div>

      <ScrollArea className="h-screen w-full">
        <div className="relative z-10 px-6 pb-8">
          {/* Header */}
          <motion.div
            initial={{ opacity: 0, y: -50 }}
            animate={{ opacity: 1, y: 0 }}
            className="pt-12 mb-8"
          >
            <div className="flex items-center justify-between mb-6">
              <motion.button
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                onClick={onBack}
                className="w-12 h-12 rounded-full flex items-center justify-center border border-white/20"
                style={{
                  background: 'rgba(255, 255, 255, 0.1)',
                  backdropFilter: 'blur(20px)'
                }}
              >
                <ArrowLeft className="w-6 h-6 text-white" />
              </motion.button>

              <div className="text-center">
                <h1 
                  className="text-3xl font-black text-white mb-2"
                  style={{ textShadow: '0 0 20px #FFD700' }}
                >
                  TROPHY ROOM
                </h1>
                <p className="text-[#B8BCC8]">Your achievements showcase</p>
              </div>

              <div className="w-12" />
            </div>

            {/* Progress Overview */}
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.2 }}
              className="grid grid-cols-3 gap-4 mb-6"
            >
              {/* Trophies Unlocked */}
              <div 
                className="p-4 rounded-2xl border border-white/10 text-center"
                style={{
                  background: 'linear-gradient(145deg, rgba(255, 215, 0, 0.1), rgba(255, 165, 0, 0.1))',
                  backdropFilter: 'blur(15px)'
                }}
              >
                <Trophy className="w-8 h-8 text-[#FFD700] mx-auto mb-2" />
                <div className="text-2xl font-black text-white">{unlockedTrophies}</div>
                <div className="text-[#B8BCC8] text-xs">Unlocked</div>
              </div>

              {/* Total XP */}
              <div 
                className="p-4 rounded-2xl border border-white/10 text-center"
                style={{
                  background: 'linear-gradient(145deg, rgba(139, 92, 246, 0.1), rgba(168, 85, 247, 0.1))',
                  backdropFilter: 'blur(15px)'
                }}
              >
                <Star className="w-8 h-8 text-[#8B5CF6] mx-auto mb-2" />
                <div className="text-2xl font-black text-white">{totalXP}</div>
                <div className="text-[#B8BCC8] text-xs">Total XP</div>
              </div>

              {/* Completion */}
              <div 
                className="p-4 rounded-2xl border border-white/10 text-center"
                style={{
                  background: 'linear-gradient(145deg, rgba(57, 255, 20, 0.1), rgba(0, 255, 136, 0.1))',
                  backdropFilter: 'blur(15px)'
                }}
              >
                <Target className="w-8 h-8 text-[#39FF14] mx-auto mb-2" />
                <div className="text-2xl font-black text-white">{Math.round((unlockedTrophies / trophies.length) * 100)}%</div>
                <div className="text-[#B8BCC8] text-xs">Complete</div>
              </div>
            </motion.div>
          </motion.div>

          {/* Trophies Grid */}
          <div className="grid grid-cols-1 gap-4">
            {trophies.map((trophy, index) => {
              const IconComponent = trophy.icon;
              return (
                <motion.div
                  key={trophy.id}
                  initial={{ opacity: 0, y: 50 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.3 + index * 0.1 }}
                  className={`relative ${!trophy.unlocked ? 'opacity-75' : ''}`}
                >
                  <div 
                    className={`p-6 rounded-2xl border transition-all duration-300 ${
                      trophy.unlocked 
                        ? 'border-white/20' 
                        : 'border-gray-500/30'
                    }`}
                    style={{
                      background: trophy.unlocked 
                        ? `linear-gradient(145deg, ${getRarityGlow(trophy.rarity)}, rgba(255, 255, 255, 0.05))`
                        : 'rgba(255, 255, 255, 0.02)',
                      backdropFilter: 'blur(20px)',
                      boxShadow: trophy.unlocked 
                        ? `0 0 20px ${getRarityGlow(trophy.rarity)}, 0 8px 32px rgba(0, 0, 0, 0.3)`
                        : '0 4px 20px rgba(0, 0, 0, 0.4)'
                    }}
                  >
                    <div className="flex items-start space-x-4">
                      {/* Trophy Icon */}
                      <div 
                        className={`w-16 h-16 rounded-2xl flex items-center justify-center ${
                          trophy.unlocked ? '' : 'grayscale'
                        }`}
                        style={{
                          background: trophy.unlocked 
                            ? `linear-gradient(145deg, ${getRarityColor(trophy.rarity)}, rgba(255, 255, 255, 0.1))`
                            : 'rgba(255, 255, 255, 0.1)',
                          boxShadow: trophy.unlocked 
                            ? `0 0 15px ${getRarityGlow(trophy.rarity)}`
                            : 'none'
                        }}
                      >
                        <IconComponent 
                          className="w-8 h-8" 
                          style={{ 
                            color: trophy.unlocked ? getRarityColor(trophy.rarity) : '#9CA3AF' 
                          }} 
                        />
                      </div>

                      {/* Trophy Info */}
                      <div className="flex-1">
                        <div className="flex items-center justify-between mb-2">
                          <h3 className={`text-lg font-bold ${trophy.unlocked ? 'text-white' : 'text-gray-400'}`}>
                            {trophy.title}
                          </h3>
                          {trophy.unlocked && (
                            <div className="flex items-center space-x-1">
                              <span className="text-[#FFD700] text-sm font-bold">+{trophy.xp}</span>
                              <Star className="w-4 h-4 text-[#FFD700]" />
                            </div>
                          )}
                        </div>
                        
                        <p className={`text-sm mb-3 ${trophy.unlocked ? 'text-[#B8BCC8]' : 'text-gray-500'}`}>
                          {trophy.description}
                        </p>

                        {/* Rarity Badge */}
                        <div className="flex items-center justify-between">
                          <div 
                            className="inline-flex items-center space-x-1 px-3 py-1 rounded-full text-xs font-medium"
                            style={{
                              background: `${getRarityColor(trophy.rarity)}20`,
                              color: getRarityColor(trophy.rarity),
                              border: `1px solid ${getRarityColor(trophy.rarity)}40`
                            }}
                          >
                            <div 
                              className="w-2 h-2 rounded-full"
                              style={{ backgroundColor: getRarityColor(trophy.rarity) }}
                            />
                            <span className="capitalize">{trophy.rarity}</span>
                          </div>

                          {trophy.unlocked && trophy.unlockedDate && (
                            <span className="text-[#B8BCC8] text-xs">
                              Unlocked {new Date(trophy.unlockedDate).toLocaleDateString()}
                            </span>
                          )}
                        </div>

                        {/* Progress Bar for locked trophies */}
                        {!trophy.unlocked && trophy.progress > 0 && (
                          <div className="mt-4">
                            <div className="flex items-center justify-between mb-2">
                              <span className="text-[#B8BCC8] text-sm">Progress</span>
                              <span className="text-white font-bold text-sm">{trophy.progress}%</span>
                            </div>
                            <div className="h-2 bg-white/10 rounded-full overflow-hidden">
                              <motion.div
                                className="h-full rounded-full"
                                style={{
                                  background: `linear-gradient(90deg, ${getRarityColor(trophy.rarity)}, rgba(255, 255, 255, 0.2))`
                                }}
                                initial={{ width: 0 }}
                                animate={{ width: `${trophy.progress}%` }}
                                transition={{ delay: 0.5 + index * 0.1, duration: 0.5 }}
                              />
                            </div>
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Unlock Animation */}
                    {trophy.unlocked && (
                      <motion.div
                        className="absolute -top-2 -right-2 w-8 h-8 rounded-full flex items-center justify-center"
                        style={{
                          background: 'linear-gradient(45deg, #39FF14, #00FF88)',
                          boxShadow: '0 0 15px rgba(57, 255, 20, 0.5)'
                        }}
                        animate={{ rotate: 360 }}
                        transition={{ duration: 3, repeat: Infinity, ease: "linear" }}
                      >
                        <span className="text-black font-bold text-sm">✓</span>
                      </motion.div>
                    )}
                  </div>
                </motion.div>
              );
            })}
          </div>
        </div>
      </ScrollArea>
    </div>
  );
};

export default TrophiesPage;
