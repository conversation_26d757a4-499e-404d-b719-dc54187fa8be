
import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { ScrollArea } from '@/components/ui/scroll-area';
import { ArrowLeft, User, Mail, Trophy, Clock, Target, TrendingUp, LogOut, Smartphone, Apple } from 'lucide-react';

interface PlayerAccountProps {
  onBack: () => void;
}

const PlayerAccount = ({ onBack }: PlayerAccountProps) => {
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [loginMethod, setLoginMethod] = useState<'guest' | 'google' | 'apple'>('guest');

  const playerStats = {
    username: 'MentalAthlete_2024',
    email: '<EMAIL>',
    level: 15,
    xp: 7450,
    xpToNext: 8000,
    gamesPlayed: 156,
    bestScore: 4750,
    totalTime: '24h 32m',
    achievements: 12,
    weeklyStreak: 7,
    mentalWellness: 85
  };

  const handleGoogleLogin = () => {
    setIsLoggedIn(true);
    setLoginMethod('google');
  };

  const handleAppleLogin = () => {
    setIsLoggedIn(true);
    setLoginMethod('apple');
  };

  const handleGuestPlay = () => {
    setLoginMethod('guest');
  };

  const handleLogout = () => {
    setIsLoggedIn(false);
    setLoginMethod('guest');
  };

  return (
    <div className="min-h-screen w-full relative overflow-hidden" style={{
      background: 'linear-gradient(135deg, #0A0B1A 0%, #161B33 50%, #0D1117 100%)'
    }}>
      {/* Animated Background */}
      <div className="absolute inset-0 overflow-hidden">
        {[...Array(30)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-2 h-2 bg-white/10 rounded-full"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
            animate={{
              opacity: [0.1, 0.6, 0.1],
              scale: [1, 2, 1],
            }}
            transition={{
              duration: 3 + Math.random() * 2,
              repeat: Infinity,
              delay: Math.random() * 2,
            }}
          />
        ))}
      </div>

      <ScrollArea className="h-screen w-full">
        <div className="relative z-10 px-6 pb-8">
          {/* Header */}
          <motion.div
            initial={{ opacity: 0, y: -50 }}
            animate={{ opacity: 1, y: 0 }}
            className="pt-12 mb-8"
          >
            <div className="flex items-center justify-between mb-6">
              <motion.button
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                onClick={onBack}
                className="w-12 h-12 rounded-full flex items-center justify-center border border-white/20"
                style={{
                  background: 'rgba(255, 255, 255, 0.1)',
                  backdropFilter: 'blur(20px)'
                }}
              >
                <ArrowLeft className="w-6 h-6 text-white" />
              </motion.button>

              <div className="text-center">
                <h1 
                  className="text-3xl font-black text-white mb-2"
                  style={{ textShadow: '0 0 20px #00D4FF' }}
                >
                  PLAYER ACCOUNT
                </h1>
                <p className="text-[#B8BCC8]">Manage your profile & progress</p>
              </div>

              <div className="w-12" />
            </div>
          </motion.div>

          {/* Account Content */}
          {!isLoggedIn ? (
            /* Login Section */
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              className="space-y-6"
            >
              {/* Login Options */}
              <div 
                className="p-8 rounded-3xl border border-white/10"
                style={{
                  background: 'rgba(255, 255, 255, 0.05)',
                  backdropFilter: 'blur(20px)',
                  boxShadow: '0 8px 32px rgba(0, 0, 0, 0.3)'
                }}
              >
                <div className="text-center mb-8">
                  <motion.div
                    animate={{ scale: [1, 1.1, 1] }}
                    transition={{ duration: 2, repeat: Infinity }}
                    className="text-6xl mb-4"
                  >
                    👤
                  </motion.div>
                  <h2 className="text-2xl font-bold text-white mb-2">Welcome to ScrollFit</h2>
                  <p className="text-[#B8BCC8]">Sign in to save your progress and compete globally</p>
                </div>

                <div className="space-y-4">
                  {/* Google Play Login */}
                  <motion.button
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    onClick={handleGoogleLogin}
                    className="w-full p-4 rounded-2xl border border-white/10 flex items-center justify-center space-x-3"
                    style={{
                      background: 'linear-gradient(135deg, rgba(66, 133, 244, 0.9), rgba(52, 168, 83, 0.9))',
                      backdropFilter: 'blur(15px)'
                    }}
                  >
                    <Smartphone className="w-6 h-6 text-white" />
                    <span className="text-white font-bold text-lg">Continue with Google Play</span>
                  </motion.button>

                  {/* Apple Game Center Login */}
                  <motion.button
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    onClick={handleAppleLogin}
                    className="w-full p-4 rounded-2xl border border-white/10 flex items-center justify-center space-x-3"
                    style={{
                      background: 'linear-gradient(135deg, rgba(0, 0, 0, 0.9), rgba(99, 99, 102, 0.9))',
                      backdropFilter: 'blur(15px)'
                    }}
                  >
                    <Apple className="w-6 h-6 text-white" />
                    <span className="text-white font-bold text-lg">Continue with Apple</span>
                  </motion.button>

                  {/* Guest Account */}
                  <motion.button
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    onClick={handleGuestPlay}
                    className="w-full p-4 rounded-2xl border border-white/20 flex items-center justify-center space-x-3"
                    style={{
                      background: 'rgba(255, 255, 255, 0.1)',
                      backdropFilter: 'blur(15px)'
                    }}
                  >
                    <User className="w-6 h-6 text-white" />
                    <span className="text-white font-bold text-lg">Continue as Guest</span>
                  </motion.button>
                </div>
              </div>
            </motion.div>
          ) : (
            /* Logged In Profile */
            <motion.div
              initial={{ opacity: 0, y: 50 }}
              animate={{ opacity: 1, y: 0 }}
              className="space-y-6"
            >
              {/* Profile Header */}
              <div 
                className="p-6 rounded-3xl border border-white/10"
                style={{
                  background: 'linear-gradient(145deg, rgba(57, 255, 20, 0.1), rgba(0, 255, 136, 0.1))',
                  backdropFilter: 'blur(20px)',
                  boxShadow: '0 8px 32px rgba(0, 0, 0, 0.3)'
                }}
              >
                <div className="flex items-center space-x-4 mb-6">
                  <div 
                    className="w-16 h-16 rounded-full flex items-center justify-center"
                    style={{
                      background: 'linear-gradient(45deg, #39FF14, #00FF88)',
                      boxShadow: '0 0 30px rgba(57, 255, 20, 0.5)'
                    }}
                  >
                    <User className="w-8 h-8 text-black" />
                  </div>
                  <div className="flex-1">
                    <h3 className="text-xl font-bold text-white">{playerStats.username}</h3>
                    <p className="text-[#B8BCC8] text-sm">{playerStats.email}</p>
                    <div className="flex items-center space-x-2 mt-1">
                      <span className="text-[#39FF14] font-bold">Level {playerStats.level}</span>
                      <span className="text-[#B8BCC8]">•</span>
                      <span className="text-[#00D4FF]">{playerStats.mentalWellness}% Wellness</span>
                    </div>
                  </div>
                  <motion.button
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                    onClick={handleLogout}
                    className="w-10 h-10 rounded-full flex items-center justify-center border border-white/20"
                    style={{ background: 'rgba(255, 255, 255, 0.1)' }}
                  >
                    <LogOut className="w-5 h-5 text-white" />
                  </motion.button>
                </div>

                {/* XP Progress Bar */}
                <div className="mb-4">
                  <div className="flex justify-between text-sm text-[#B8BCC8] mb-2">
                    <span>Experience Points</span>
                    <span>{playerStats.xp} / {playerStats.xpToNext} XP</span>
                  </div>
                  <div className="h-3 bg-white/10 rounded-full overflow-hidden">
                    <motion.div
                      className="h-full rounded-full"
                      style={{
                        background: 'linear-gradient(90deg, #39FF14, #00FF88)',
                        width: `${(playerStats.xp / playerStats.xpToNext) * 100}%`
                      }}
                      initial={{ width: 0 }}
                      animate={{ width: `${(playerStats.xp / playerStats.xpToNext) * 100}%` }}
                      transition={{ duration: 1, ease: "easeOut" }}
                    />
                  </div>
                </div>
              </div>

              {/* Stats Grid */}
              <div className="grid grid-cols-2 gap-4">
                <div 
                  className="p-4 rounded-2xl border border-white/10"
                  style={{
                    background: 'rgba(255, 255, 255, 0.05)',
                    backdropFilter: 'blur(15px)'
                  }}
                >
                  <Trophy className="w-6 h-6 text-[#FFD700] mb-2" />
                  <div className="text-2xl font-black text-white">{playerStats.bestScore}</div>
                  <div className="text-[#B8BCC8] text-sm">Best Score</div>
                </div>

                <div 
                  className="p-4 rounded-2xl border border-white/10"
                  style={{
                    background: 'rgba(255, 255, 255, 0.05)',
                    backdropFilter: 'blur(15px)'
                  }}
                >
                  <Target className="w-6 h-6 text-[#00D4FF] mb-2" />
                  <div className="text-2xl font-black text-white">{playerStats.gamesPlayed}</div>
                  <div className="text-[#B8BCC8] text-sm">Games Played</div>
                </div>

                <div 
                  className="p-4 rounded-2xl border border-white/10"
                  style={{
                    background: 'rgba(255, 255, 255, 0.05)',
                    backdropFilter: 'blur(15px)'
                  }}
                >
                  <Clock className="w-6 h-6 text-[#FF6B35] mb-2" />
                  <div className="text-2xl font-black text-white">{playerStats.totalTime}</div>
                  <div className="text-[#B8BCC8] text-sm">Time Played</div>
                </div>

                <div 
                  className="p-4 rounded-2xl border border-white/10"
                  style={{
                    background: 'rgba(255, 255, 255, 0.05)',
                    backdropFilter: 'blur(15px)'
                  }}
                >
                  <TrendingUp className="w-6 h-6 text-[#39FF14] mb-2" />
                  <div className="text-2xl font-black text-white">{playerStats.weeklyStreak}</div>
                  <div className="text-[#B8BCC8] text-sm">Day Streak</div>
                </div>
              </div>

              {/* Account Management */}
              <div 
                className="p-6 rounded-2xl border border-white/10"
                style={{
                  background: 'rgba(255, 255, 255, 0.05)',
                  backdropFilter: 'blur(15px)'
                }}
              >
                <h3 className="text-xl font-bold text-white mb-4">Account Management</h3>
                <div className="space-y-3">
                  <div className="flex items-center justify-between py-2">
                    <span className="text-[#B8BCC8]">Connected Account</span>
                    <span className="text-white font-medium">
                      {loginMethod === 'google' ? 'Google Play Games' : 
                       loginMethod === 'apple' ? 'Apple Game Center' : 'Guest Account'}
                    </span>
                  </div>
                  <div className="flex items-center justify-between py-2">
                    <span className="text-[#B8BCC8]">Data Sync</span>
                    <span className="text-[#39FF14] font-medium">Enabled</span>
                  </div>
                  <div className="flex items-center justify-between py-2">
                    <span className="text-[#B8BCC8]">Cloud Backup</span>
                    <span className="text-[#39FF14] font-medium">Active</span>
                  </div>
                </div>
              </div>
            </motion.div>
          )}
        </div>
      </ScrollArea>
    </div>
  );
};

export default PlayerAccount;
