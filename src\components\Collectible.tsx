
import React from 'react';
import { motion } from 'framer-motion';

interface CollectibleProps {
  type: 'apple' | 'banana' | 'carrot' | 'broccoli' | 'watermelon' | 'eggplant' | 'anxiety' | 'worry' | 'failure' | 'obstacle';
  position: { x: number; y: number };
  onCollect: () => void;
  isCollected: boolean;
}

const Collectible = ({ type, position, onCollect, isCollected }: CollectibleProps) => {
  const getCollectibleData = () => {
    // Positive collectibles (from sprite images)
    switch (type) {
      case 'apple':
        return { 
          emoji: '🍎', 
          name: 'HEALTHY NUTRITION',
          color: '#FF4757', 
          points: 15,
          glowColor: 'rgba(255, 71, 87, 0.6)',
          isPositive: true
        };
      case 'banana':
        return { 
          emoji: '🍌', 
          name: 'ENERGY BOOST',
          color: '#FFA502', 
          points: 20,
          glowColor: 'rgba(255, 165, 2, 0.6)',
          isPositive: true
        };
      case 'carrot':
        return { 
          emoji: '🥕', 
          name: 'VISION CLARITY',
          color: '#FF8C00', 
          points: 25,
          glowColor: 'rgba(255, 140, 0, 0.6)',
          isPositive: true
        };
      case 'broccoli':
        return { 
          emoji: '🥦', 
          name: 'BRAIN POWER',
          color: '#2ED573', 
          points: 30,
          glowColor: 'rgba(46, 213, 115, 0.6)',
          isPositive: true
        };
      case 'watermelon':
        return { 
          emoji: '🍉', 
          name: 'HYDRATION',
          color: '#FF6B6B', 
          points: 35,
          glowColor: 'rgba(255, 107, 107, 0.6)',
          isPositive: true
        };
      case 'eggplant':
        return { 
          emoji: '🍆', 
          name: 'ANTIOXIDANTS',
          color: '#6C5CE7', 
          points: 40,
          glowColor: 'rgba(108, 92, 231, 0.6)',
          isPositive: true
        };
      
      // Negative obstacles (from sprite images)
      case 'anxiety':
        return { 
          emoji: '😰', 
          name: 'ANXIETY CLOUD',
          color: '#74B9FF', 
          points: -15,
          glowColor: 'rgba(116, 185, 255, 0.6)',
          isPositive: false
        };
      case 'worry':
        return { 
          emoji: '😟', 
          name: 'WORRY THOUGHTS',
          color: '#A29BFE', 
          points: -15,
          glowColor: 'rgba(162, 155, 254, 0.6)',
          isPositive: false
        };
      case 'failure':
        return { 
          emoji: '💢', 
          name: 'FAILURE MINDSET',
          color: '#FD79A8', 
          points: -15,
          glowColor: 'rgba(253, 121, 168, 0.6)',
          isPositive: false
        };
      case 'obstacle':
        return { 
          emoji: '🚧', 
          name: 'MENTAL BLOCK',
          color: '#636E72', 
          points: -15,
          glowColor: 'rgba(99, 110, 114, 0.6)',
          isPositive: false
        };
      
      default:
        return { 
          emoji: '🍎', 
          name: 'HEALTHY NUTRITION',
          color: '#FF4757', 
          points: 15,
          glowColor: 'rgba(255, 71, 87, 0.6)',
          isPositive: true
        };
    }
  };

  const collectibleData = getCollectibleData();

  return (
    <motion.div
      className="absolute z-20 cursor-pointer"
      style={{
        left: `${position.x}%`,
        top: `${position.y}%`,
        transform: 'translate(-50%, -50%)'
      }}
      initial={{ scale: 0, y: -20, opacity: 0 }}
      animate={isCollected ? {
        scale: [1, 1.5, 0], 
        y: [0, -30, -60],
        opacity: [1, 1, 0],
        rotateZ: [0, 180, 360]
      } : { 
        scale: 1, 
        y: [0, -8, 0],
        rotateZ: [0, 5, -5, 0],
        opacity: 1
      }}
      exit={{ 
        scale: 0, 
        opacity: 0
      }}
      transition={isCollected ? {
        duration: 0.8,
        ease: "easeOut"
      } : {
        scale: { duration: 0.3 },
        y: { duration: 2, repeat: Infinity, ease: "easeInOut" },
        rotateZ: { duration: 1.5, repeat: Infinity, ease: "easeInOut" },
        opacity: { duration: 0.2 }
      }}
      onClick={onCollect}
      whileHover={{ scale: 1.2 }}
      whileTap={{ scale: 0.8 }}
    >
      {/* Enhanced Glow Effect */}
      <motion.div 
        className="absolute inset-0 rounded-full"
        style={{
          background: `radial-gradient(circle, ${collectibleData.color}80 0%, ${collectibleData.color}40 40%, transparent 70%)`,
          transform: 'scale(4)',
          filter: 'blur(15px)'
        }}
        animate={{
          scale: [4, 4.5, 4],
          opacity: [0.6, 1, 0.6]
        }}
        transition={{
          duration: 2,
          repeat: Infinity,
          ease: "easeInOut"
        }}
      />
      
      {/* Main Collectible Container with Enhanced Styling */}
      <motion.div 
        className="relative w-20 h-20 rounded-full flex items-center justify-center shadow-2xl border-4"
        style={{
          background: collectibleData.isPositive 
            ? `radial-gradient(circle at 30% 30%, rgba(255,255,255,0.9), transparent 40%), 
               radial-gradient(circle at 70% 70%, ${collectibleData.color}60, ${collectibleData.color}),
               linear-gradient(135deg, ${collectibleData.color}E0, ${collectibleData.color})`
            : `radial-gradient(circle at 30% 30%, rgba(0,0,0,0.3), transparent 40%), 
               radial-gradient(circle at 70% 70%, ${collectibleData.color}80, ${collectibleData.color}),
               linear-gradient(135deg, ${collectibleData.color}C0, ${collectibleData.color})`,
          borderColor: collectibleData.isPositive ? 'rgba(255,255,255,0.8)' : 'rgba(255,0,0,0.6)',
          boxShadow: `
            0 0 40px ${collectibleData.glowColor},
            0 12px 40px rgba(0,0,0,0.4),
            inset 0 3px 0 rgba(255,255,255,0.4)
          `,
          filter: collectibleData.isPositive 
            ? 'drop-shadow(0 0 20px rgba(255,255,255,0.3))'
            : 'drop-shadow(0 0 20px rgba(255,0,0,0.3))'
        }}
        animate={!collectibleData.isPositive ? {
          boxShadow: [
            `0 0 40px ${collectibleData.glowColor}, 0 12px 40px rgba(0,0,0,0.4)`,
            `0 0 60px ${collectibleData.glowColor}, 0 12px 40px rgba(0,0,0,0.4)`,
            `0 0 40px ${collectibleData.glowColor}, 0 12px 40px rgba(0,0,0,0.4)`
          ]
        } : {}}
        transition={{ duration: 1.5, repeat: Infinity }}
      >
        {/* Enhanced Emoji with better effects */}
        <span 
          className="text-5xl relative z-10"
          style={{
            filter: `drop-shadow(3px 3px 6px rgba(0,0,0,0.5)) ${collectibleData.isPositive ? 'drop-shadow(0 0 10px rgba(255,255,255,0.8))' : ''}`,
            textShadow: collectibleData.isPositive 
              ? '0 0 15px rgba(255,255,255,0.9)' 
              : '0 0 15px rgba(255,0,0,0.7)'
          }}
        >
          {collectibleData.emoji}
        </span>

        {/* Negative item warning indicator */}
        {!collectibleData.isPositive && (
          <motion.div
            className="absolute -top-2 -right-2 w-8 h-8 bg-red-500 rounded-full flex items-center justify-center"
            animate={{ scale: [1, 1.3, 1] }}
            transition={{ duration: 0.5, repeat: Infinity }}
            style={{
              boxShadow: '0 0 20px rgba(255, 0, 0, 0.7)',
              border: '2px solid rgba(255,255,255,0.8)'
            }}
          >
            <span className="text-white font-bold text-lg">!</span>
          </motion.div>
        )}
      </motion.div>

      {/* Enhanced Points Display */}
      <motion.div
        className="absolute -top-16 left-1/2 transform -translate-x-1/2"
        initial={{ opacity: 0, scale: 0, y: 10 }}
        animate={{ opacity: 1, scale: 1, y: 0 }}
        transition={{ delay: 0.1, duration: 0.3 }}
      >
        <div 
          className="px-5 py-2 rounded-full shadow-xl border-3"
          style={{
            background: collectibleData.isPositive 
              ? 'linear-gradient(135deg, #10B981, #059669)' 
              : 'linear-gradient(135deg, #EF4444, #DC2626)',
            borderColor: 'rgba(255,255,255,0.6)',
            backdropFilter: 'blur(15px)',
            boxShadow: '0 8px 25px rgba(0,0,0,0.3)'
          }}
        >
          <span className="text-white font-black text-lg drop-shadow-lg">
            {collectibleData.points > 0 ? '+' : ''}{collectibleData.points}
          </span>
        </div>
      </motion.div>

      {/* PREMIUM BUBBLE COLLECTION EFFECT */}
      {isCollected && (
        <div className="absolute inset-0 pointer-events-none">
          {/* Main bubble burst effect */}
          <motion.div
            className="absolute inset-0 rounded-full border-4"
            style={{ 
              background: `${collectibleData.color}40`,
              borderColor: collectibleData.color
            }}
            initial={{ scale: 1, opacity: 1 }}
            animate={{ 
              scale: [1, 4, 6], 
              opacity: [1, 0.6, 0],
              borderWidth: [4, 2, 0]
            }}
            transition={{ duration: 0.8, ease: "easeOut" }}
          />
          
          {/* Rainbow confetti burst for positive items */}
          {collectibleData.isPositive && [...Array(20)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute w-3 h-3 rounded-full"
              style={{
                background: ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FCEA2B', '#FF9FF3'][i % 6],
                left: '50%',
                top: '50%'
              }}
              initial={{ scale: 0, x: 0, y: 0, opacity: 1 }}
              animate={{
                scale: [0, 1, 0.5, 0],
                x: Math.cos(i * (360 / 20) * Math.PI / 180) * (60 + Math.random() * 40),
                y: Math.sin(i * (360 / 20) * Math.PI / 180) * (60 + Math.random() * 40),
                opacity: [1, 1, 0.5, 0]
              }}
              transition={{ duration: 1.2, ease: "easeOut" }}
            />
          ))}
          
          {/* Score popup with bubble effect */}
          <motion.div
            className="absolute -top-20 left-1/2 transform -translate-x-1/2"
            initial={{ opacity: 0, scale: 0, y: 0 }}
            animate={{ 
              opacity: [0, 1, 1, 0], 
              scale: [0, 1.8, 1.4, 0], 
              y: [0, -40, -70, -100] 
            }}
            transition={{ duration: 1.5, ease: "easeOut" }}
          >
            <div 
              className="px-6 py-3 rounded-full text-white font-black text-2xl shadow-2xl border-3 border-white/50"
              style={{
                background: collectibleData.isPositive 
                  ? 'linear-gradient(135deg, #10B981, #059669)' 
                  : 'linear-gradient(135deg, #EF4444, #DC2626)',
                textShadow: '2px 2px 6px rgba(0,0,0,0.8)',
                filter: 'drop-shadow(0 0 20px rgba(255,255,255,0.5))'
              }}
            >
              {collectibleData.points > 0 ? '+' : ''}{collectibleData.points}
            </div>
          </motion.div>

          {/* Sparkle effects around the bubble */}
          {[...Array(8)].map((_, i) => (
            <motion.div
              key={`sparkle-${i}`}
              className="absolute w-2 h-2"
              style={{
                background: '#FFD700',
                borderRadius: '50%',
                left: '50%',
                top: '50%',
                filter: 'drop-shadow(0 0 6px #FFD700)'
              }}
              initial={{ scale: 0, x: 0, y: 0, opacity: 1 }}
              animate={{
                scale: [0, 1, 0],
                x: Math.cos(i * 45 * Math.PI / 180) * 80,
                y: Math.sin(i * 45 * Math.PI / 180) * 80,
                opacity: [0, 1, 0]
              }}
              transition={{ 
                duration: 1, 
                ease: "easeOut",
                delay: 0.2 + i * 0.05
              }}
            />
          ))}
        </div>
      )}
    </motion.div>
  );
};

export default Collectible;
