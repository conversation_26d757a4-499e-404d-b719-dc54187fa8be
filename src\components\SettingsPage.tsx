
import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { ScrollArea } from '@/components/ui/scroll-area';
import { ArrowLeft, Volume2, Vibrate, Palette, Shield, Smartphone, Globe, Bell, User } from 'lucide-react';

interface SettingsPageProps {
  onBack: () => void;
}

const SettingsPage = ({ onBack }: SettingsPageProps) => {
  const [settings, setSettings] = useState({
    musicVolume: 75,
    sfxVolume: 85,
    hapticFeedback: true,
    notifications: true,
    darkMode: true,
    autoSave: true,
    dataSync: false,
    difficulty: 'normal'
  });

  const updateSetting = (key: string, value: any) => {
    setSettings(prev => ({ ...prev, [key]: value }));
  };

  const ToggleSwitch = ({ enabled, onChange }: { enabled: boolean; onChange: (value: boolean) => void }) => (
    <motion.div
      className={`relative w-12 h-6 rounded-full cursor-pointer ${
        enabled ? 'bg-[#39FF14]' : 'bg-white/20'
      }`}
      onClick={() => onChange(!enabled)}
      style={{
        boxShadow: enabled ? '0 0 15px rgba(57, 255, 20, 0.3)' : 'none'
      }}
    >
      <motion.div
        className={`absolute top-1 w-4 h-4 rounded-full ${
          enabled ? 'bg-black' : 'bg-white'
        }`}
        animate={{
          x: enabled ? 24 : 4
        }}
        transition={{ duration: 0.2 }}
      />
    </motion.div>
  );

  const Slider = ({ value, onChange, max = 100 }: { value: number; onChange: (value: number) => void; max?: number }) => (
    <div className="relative w-full h-6 flex items-center">
      <div className="w-full h-2 bg-white/20 rounded-full">
        <div
          className="h-full rounded-full"
          style={{
            width: `${(value / max) * 100}%`,
            background: 'linear-gradient(90deg, #39FF14, #00FF88)',
            boxShadow: '0 0 10px rgba(57, 255, 20, 0.3)'
          }}
        />
      </div>
      <input
        type="range"
        min={0}
        max={max}
        value={value}
        onChange={(e) => onChange(parseInt(e.target.value))}
        className="absolute w-full h-2 opacity-0 cursor-pointer"
      />
      <div
        className="absolute w-4 h-4 bg-white rounded-full border-2 border-[#39FF14] shadow-lg"
        style={{
          left: `calc(${(value / max) * 100}% - 8px)`,
          boxShadow: '0 0 10px rgba(57, 255, 20, 0.5)'
        }}
      />
    </div>
  );

  return (
    <div className="min-h-screen w-full relative overflow-hidden" style={{
      background: 'linear-gradient(135deg, #0A0B1A 0%, #161B33 50%, #0D1117 100%)'
    }}>
      {/* Animated Background */}
      <div className="absolute inset-0 overflow-hidden">
        {[...Array(30)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-2 h-2 bg-white/10 rounded-full"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
            animate={{
              opacity: [0.1, 0.6, 0.1],
              scale: [1, 2, 1],
            }}
            transition={{
              duration: 3 + Math.random() * 2,
              repeat: Infinity,
              delay: Math.random() * 2,
            }}
          />
        ))}
      </div>

      <ScrollArea className="h-screen w-full">
        <div className="relative z-10 px-6 pb-8">
          {/* Header */}
          <motion.div
            initial={{ opacity: 0, y: -50 }}
            animate={{ opacity: 1, y: 0 }}
            className="pt-12 mb-8"
          >
            <div className="flex items-center justify-between mb-6">
              <motion.button
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                onClick={onBack}
                className="w-12 h-12 rounded-full flex items-center justify-center border border-white/20"
                style={{
                  background: 'rgba(255, 255, 255, 0.1)',
                  backdropFilter: 'blur(20px)'
                }}
              >
                <ArrowLeft className="w-6 h-6 text-white" />
              </motion.button>

              <div className="text-center">
                <h1 
                  className="text-3xl font-black text-white mb-2"
                  style={{ textShadow: '0 0 20px #9CA3AF' }}
                >
                  SETTINGS
                </h1>
                <p className="text-[#B8BCC8]">Customize your experience</p>
              </div>

              <div className="w-12" />
            </div>
          </motion.div>

          {/* Audio Settings */}
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="mb-6"
          >
            <div 
              className="p-6 rounded-2xl border border-white/10"
              style={{
                background: 'rgba(255, 255, 255, 0.05)',
                backdropFilter: 'blur(20px)',
                boxShadow: '0 8px 32px rgba(0, 0, 0, 0.3)'
              }}
            >
              <div className="flex items-center space-x-3 mb-6">
                <Volume2 className="w-6 h-6 text-[#00D4FF]" />
                <h3 className="text-xl font-bold text-white">Audio</h3>
              </div>
              
              <div className="space-y-6">
                {/* Music Volume */}
                <div>
                  <div className="flex items-center justify-between mb-3">
                    <span className="text-white font-medium">Music Volume</span>
                    <span className="text-[#00D4FF] font-bold">{settings.musicVolume}%</span>
                  </div>
                  <Slider 
                    value={settings.musicVolume} 
                    onChange={(value) => updateSetting('musicVolume', value)} 
                  />
                </div>
                
                {/* SFX Volume */}
                <div>
                  <div className="flex items-center justify-between mb-3">
                    <span className="text-white font-medium">Sound Effects</span>
                    <span className="text-[#00D4FF] font-bold">{settings.sfxVolume}%</span>
                  </div>
                  <Slider 
                    value={settings.sfxVolume} 
                    onChange={(value) => updateSetting('sfxVolume', value)} 
                  />
                </div>
              </div>
            </div>
          </motion.div>

          {/* Controls Settings */}
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="mb-6"
          >
            <div 
              className="p-6 rounded-2xl border border-white/10"
              style={{
                background: 'rgba(255, 255, 255, 0.05)',
                backdropFilter: 'blur(20px)',
                boxShadow: '0 8px 32px rgba(0, 0, 0, 0.3)'
              }}
            >
              <div className="flex items-center space-x-3 mb-6">
                <Smartphone className="w-6 h-6 text-[#39FF14]" />
                <h3 className="text-xl font-bold text-white">Controls</h3>
              </div>
              
              <div className="space-y-4">
                {/* Haptic Feedback */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <Vibrate className="w-5 h-5 text-[#B8BCC8]" />
                    <div>
                      <span className="text-white font-medium">Haptic Feedback</span>
                      <p className="text-[#B8BCC8] text-sm">Vibration on interactions</p>
                    </div>
                  </div>
                  <ToggleSwitch 
                    enabled={settings.hapticFeedback} 
                    onChange={(value) => updateSetting('hapticFeedback', value)} 
                  />
                </div>
              </div>
            </div>
          </motion.div>

          {/* Display Settings */}
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
            className="mb-6"
          >
            <div 
              className="p-6 rounded-2xl border border-white/10"
              style={{
                background: 'rgba(255, 255, 255, 0.05)',
                backdropFilter: 'blur(20px)',
                boxShadow: '0 8px 32px rgba(0, 0, 0, 0.3)'
              }}
            >
              <div className="flex items-center space-x-3 mb-6">
                <Palette className="w-6 h-6 text-[#8B5CF6]" />
                <h3 className="text-xl font-bold text-white">Display</h3>
              </div>
              
              <div className="space-y-4">
                {/* Dark Mode */}
                <div className="flex items-center justify-between">
                  <div>
                    <span className="text-white font-medium">Dark Mode</span>
                    <p className="text-[#B8BCC8] text-sm">Dark theme interface</p>
                  </div>
                  <ToggleSwitch 
                    enabled={settings.darkMode} 
                    onChange={(value) => updateSetting('darkMode', value)} 
                  />
                </div>
              </div>
            </div>
          </motion.div>

          {/* Game Settings */}
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5 }}
            className="mb-6"
          >
            <div 
              className="p-6 rounded-2xl border border-white/10"
              style={{
                background: 'rgba(255, 255, 255, 0.05)',
                backdropFilter: 'blur(20px)',
                boxShadow: '0 8px 32px rgba(0, 0, 0, 0.3)'
              }}
            >
              <div className="flex items-center space-x-3 mb-6">
                <Shield className="w-6 h-6 text-[#FF6B35]" />
                <h3 className="text-xl font-bold text-white">Game</h3>
              </div>
              
              <div className="space-y-4">
                {/* Auto Save */}
                <div className="flex items-center justify-between">
                  <div>
                    <span className="text-white font-medium">Auto Save</span>
                    <p className="text-[#B8BCC8] text-sm">Automatically save progress</p>
                  </div>
                  <ToggleSwitch 
                    enabled={settings.autoSave} 
                    onChange={(value) => updateSetting('autoSave', value)} 
                  />
                </div>

                {/* Notifications */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <Bell className="w-5 h-5 text-[#B8BCC8]" />
                    <div>
                      <span className="text-white font-medium">Notifications</span>
                      <p className="text-[#B8BCC8] text-sm">Daily reminders and achievements</p>
                    </div>
                  </div>
                  <ToggleSwitch 
                    enabled={settings.notifications} 
                    onChange={(value) => updateSetting('notifications', value)} 
                  />
                </div>

                {/* Data Sync */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <Globe className="w-5 h-5 text-[#B8BCC8]" />
                    <div>
                      <span className="text-white font-medium">Cloud Sync</span>
                      <p className="text-[#B8BCC8] text-sm">Sync data across devices</p>
                    </div>
                  </div>
                  <ToggleSwitch 
                    enabled={settings.dataSync} 
                    onChange={(value) => updateSetting('dataSync', value)} 
                  />
                </div>
              </div>
            </div>
          </motion.div>

          {/* Account Settings */}
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6 }}
          >
            <div 
              className="p-6 rounded-2xl border border-white/10"
              style={{
                background: 'rgba(255, 255, 255, 0.05)',
                backdropFilter: 'blur(20px)',
                boxShadow: '0 8px 32px rgba(0, 0, 0, 0.3)'
              }}
            >
              <div className="flex items-center space-x-3 mb-6">
                <User className="w-6 h-6 text-[#FFD700]" />
                <h3 className="text-xl font-bold text-white">Account</h3>
              </div>
              
              <div className="space-y-4">
                {/* Reset Progress Button */}
                <motion.button
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  className="w-full p-4 rounded-xl border border-red-500/30 text-red-400 font-medium"
                  style={{
                    background: 'rgba(239, 68, 68, 0.1)',
                    backdropFilter: 'blur(15px)'
                  }}
                >
                  Reset Game Progress
                </motion.button>

                {/* Sign Out Button */}
                <motion.button
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  className="w-full p-4 rounded-xl border border-white/20 text-[#B8BCC8] font-medium"
                  style={{
                    background: 'rgba(255, 255, 255, 0.05)',
                    backdropFilter: 'blur(15px)'
                  }}
                >
                  Sign Out
                </motion.button>
              </div>
            </div>
          </motion.div>
        </div>
      </ScrollArea>
    </div>
  );
};

export default SettingsPage;
